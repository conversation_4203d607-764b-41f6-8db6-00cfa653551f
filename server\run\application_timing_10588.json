[{"name": "Process Start", "start": 1746258451949, "end": 1746258453794, "duration": 1845, "pid": 10588, "index": 0}, {"name": "Application Start", "start": 1746258453796, "end": 1746258455917, "duration": 2121, "pid": 10588, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746258453815, "end": 1746258453852, "duration": 37, "pid": 10588, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746258453852, "end": 1746258453895, "duration": 43, "pid": 10588, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746258453853, "end": 1746258453854, "duration": 1, "pid": 10588, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746258453857, "end": 1746258453857, "duration": 0, "pid": 10588, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746258453858, "end": 1746258453859, "duration": 1, "pid": 10588, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746258453859, "end": 1746258453860, "duration": 1, "pid": 10588, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746258453861, "end": 1746258453862, "duration": 1, "pid": 10588, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746258453862, "end": 1746258453863, "duration": 1, "pid": 10588, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746258453864, "end": 1746258453864, "duration": 0, "pid": 10588, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746258453865, "end": 1746258453865, "duration": 0, "pid": 10588, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746258453866, "end": 1746258453866, "duration": 0, "pid": 10588, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746258453867, "end": 1746258453868, "duration": 1, "pid": 10588, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746258453869, "end": 1746258453869, "duration": 0, "pid": 10588, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746258453870, "end": 1746258453870, "duration": 0, "pid": 10588, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746258453872, "end": 1746258453872, "duration": 0, "pid": 10588, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746258453873, "end": 1746258453874, "duration": 1, "pid": 10588, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746258453874, "end": 1746258453875, "duration": 1, "pid": 10588, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746258453875, "end": 1746258453876, "duration": 1, "pid": 10588, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746258453877, "end": 1746258453877, "duration": 0, "pid": 10588, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746258453878, "end": 1746258453878, "duration": 0, "pid": 10588, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746258453879, "end": 1746258453879, "duration": 0, "pid": 10588, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746258453880, "end": 1746258453880, "duration": 0, "pid": 10588, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746258453881, "end": 1746258453881, "duration": 0, "pid": 10588, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746258453883, "end": 1746258453883, "duration": 0, "pid": 10588, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746258453884, "end": 1746258453884, "duration": 0, "pid": 10588, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746258453886, "end": 1746258453886, "duration": 0, "pid": 10588, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746258453890, "end": 1746258453890, "duration": 0, "pid": 10588, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746258453894, "end": 1746258453895, "duration": 1, "pid": 10588, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746258453895, "end": 1746258453895, "duration": 0, "pid": 10588, "index": 30}, {"name": "Load extend/application.js", "start": 1746258453896, "end": 1746258453997, "duration": 101, "pid": 10588, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746258453897, "end": 1746258453898, "duration": 1, "pid": 10588, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746258453898, "end": 1746258453900, "duration": 2, "pid": 10588, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746258453901, "end": 1746258453908, "duration": 7, "pid": 10588, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746258453910, "end": 1746258453917, "duration": 7, "pid": 10588, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746258453918, "end": 1746258453921, "duration": 3, "pid": 10588, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746258453923, "end": 1746258453925, "duration": 2, "pid": 10588, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746258453926, "end": 1746258453989, "duration": 63, "pid": 10588, "index": 38}, {"name": "Load extend/request.js", "start": 1746258453997, "end": 1746258454014, "duration": 17, "pid": 10588, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746258454005, "end": 1746258454007, "duration": 2, "pid": 10588, "index": 40}, {"name": "Load extend/response.js", "start": 1746258454014, "end": 1746258454032, "duration": 18, "pid": 10588, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746258454022, "end": 1746258454025, "duration": 3, "pid": 10588, "index": 42}, {"name": "Load extend/context.js", "start": 1746258454032, "end": 1746258454106, "duration": 74, "pid": 10588, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746258454033, "end": 1746258454052, "duration": 19, "pid": 10588, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746258454053, "end": 1746258454056, "duration": 3, "pid": 10588, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746258454057, "end": 1746258454057, "duration": 0, "pid": 10588, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746258454059, "end": 1746258454085, "duration": 26, "pid": 10588, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746258454087, "end": 1746258454089, "duration": 2, "pid": 10588, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746258454092, "end": 1746258454092, "duration": 0, "pid": 10588, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746258454094, "end": 1746258454098, "duration": 4, "pid": 10588, "index": 50}, {"name": "Load extend/helper.js", "start": 1746258454106, "end": 1746258454150, "duration": 44, "pid": 10588, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746258454110, "end": 1746258454134, "duration": 24, "pid": 10588, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746258454141, "end": 1746258454141, "duration": 0, "pid": 10588, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746258454142, "end": 1746258454143, "duration": 1, "pid": 10588, "index": 54}, {"name": "Load app.js", "start": 1746258454150, "end": 1746258454268, "duration": 118, "pid": 10588, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746258454150, "end": 1746258454151, "duration": 1, "pid": 10588, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746258454151, "end": 1746258454155, "duration": 4, "pid": 10588, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746258454156, "end": 1746258454169, "duration": 13, "pid": 10588, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746258454170, "end": 1746258454191, "duration": 21, "pid": 10588, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746258454192, "end": 1746258454211, "duration": 19, "pid": 10588, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746258454212, "end": 1746258454213, "duration": 1, "pid": 10588, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746258454214, "end": 1746258454217, "duration": 3, "pid": 10588, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746258454217, "end": 1746258454218, "duration": 1, "pid": 10588, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746258454219, "end": 1746258454219, "duration": 0, "pid": 10588, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746258454221, "end": 1746258454223, "duration": 2, "pid": 10588, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746258454225, "end": 1746258454225, "duration": 0, "pid": 10588, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746258454226, "end": 1746258454226, "duration": 0, "pid": 10588, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746258454227, "end": 1746258454227, "duration": 0, "pid": 10588, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746258454228, "end": 1746258454237, "duration": 9, "pid": 10588, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746258454238, "end": 1746258454258, "duration": 20, "pid": 10588, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746258454259, "end": 1746258454266, "duration": 7, "pid": 10588, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746258454283, "end": 1746258455895, "duration": 1612, "pid": 10588, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746258454992, "end": 1746258455067, "duration": 75, "pid": 10588, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746258455016, "end": 1746258455802, "duration": 786, "pid": 10588, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746258455097, "end": 1746258455916, "duration": 819, "pid": 10588, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746258455200, "end": 1746258455898, "duration": 698, "pid": 10588, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746258455311, "end": 1746258455857, "duration": 546, "pid": 10588, "index": 77}, {"name": "Load Service", "start": 1746258455311, "end": 1746258455443, "duration": 132, "pid": 10588, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746258455311, "end": 1746258455443, "duration": 132, "pid": 10588, "index": 79}, {"name": "Load Middleware", "start": 1746258455444, "end": 1746258455650, "duration": 206, "pid": 10588, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746258455444, "end": 1746258455631, "duration": 187, "pid": 10588, "index": 81}, {"name": "Load Controller", "start": 1746258455651, "end": 1746258455696, "duration": 45, "pid": 10588, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746258455651, "end": 1746258455696, "duration": 45, "pid": 10588, "index": 83}, {"name": "Load Router", "start": 1746258455696, "end": 1746258455707, "duration": 11, "pid": 10588, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746258455696, "end": 1746258455697, "duration": 1, "pid": 10588, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746258455701, "end": 1746258455802, "duration": 101, "pid": 10588, "index": 86}]