[{"name": "Process Start", "start": 1748230509054, "end": 1748230512893, "duration": 3839, "pid": 24156, "index": 0}, {"name": "Application Start", "start": 1748230512895, "end": 1748230514430, "duration": 1535, "pid": 24156, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748230512921, "end": 1748230512955, "duration": 34, "pid": 24156, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748230512955, "end": 1748230513004, "duration": 49, "pid": 24156, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748230512956, "end": 1748230512957, "duration": 1, "pid": 24156, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748230512959, "end": 1748230512960, "duration": 1, "pid": 24156, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748230512960, "end": 1748230512961, "duration": 1, "pid": 24156, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748230512962, "end": 1748230512962, "duration": 0, "pid": 24156, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748230512964, "end": 1748230512964, "duration": 0, "pid": 24156, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748230512965, "end": 1748230512966, "duration": 1, "pid": 24156, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748230512967, "end": 1748230512967, "duration": 0, "pid": 24156, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748230512968, "end": 1748230512970, "duration": 2, "pid": 24156, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748230512970, "end": 1748230512971, "duration": 1, "pid": 24156, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748230512973, "end": 1748230512974, "duration": 1, "pid": 24156, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748230512975, "end": 1748230512975, "duration": 0, "pid": 24156, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748230512976, "end": 1748230512977, "duration": 1, "pid": 24156, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748230512977, "end": 1748230512978, "duration": 1, "pid": 24156, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748230512979, "end": 1748230512980, "duration": 1, "pid": 24156, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748230512981, "end": 1748230512982, "duration": 1, "pid": 24156, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748230512983, "end": 1748230512984, "duration": 1, "pid": 24156, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748230512985, "end": 1748230512986, "duration": 1, "pid": 24156, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748230512988, "end": 1748230512989, "duration": 1, "pid": 24156, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748230512989, "end": 1748230512990, "duration": 1, "pid": 24156, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748230512991, "end": 1748230512991, "duration": 0, "pid": 24156, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748230512992, "end": 1748230512992, "duration": 0, "pid": 24156, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748230512993, "end": 1748230512993, "duration": 0, "pid": 24156, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748230512995, "end": 1748230512995, "duration": 0, "pid": 24156, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748230512997, "end": 1748230512997, "duration": 0, "pid": 24156, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748230513000, "end": 1748230513000, "duration": 0, "pid": 24156, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748230513003, "end": 1748230513004, "duration": 1, "pid": 24156, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748230513004, "end": 1748230513004, "duration": 0, "pid": 24156, "index": 30}, {"name": "Load extend/agent.js", "start": 1748230513005, "end": 1748230513151, "duration": 146, "pid": 24156, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/agent.js", "start": 1748230513006, "end": 1748230513008, "duration": 2, "pid": 24156, "index": 32}, {"name": "Require(28) node_modules/egg-schedule/app/extend/agent.js", "start": 1748230513010, "end": 1748230513137, "duration": 127, "pid": 24156, "index": 33}, {"name": "Require(29) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748230513138, "end": 1748230513140, "duration": 2, "pid": 24156, "index": 34}, {"name": "Load extend/context.js", "start": 1748230513151, "end": 1748230513226, "duration": 75, "pid": 24156, "index": 35}, {"name": "Require(30) node_modules/egg-security/app/extend/context.js", "start": 1748230513152, "end": 1748230513168, "duration": 16, "pid": 24156, "index": 36}, {"name": "Require(31) node_modules/egg-jsonp/app/extend/context.js", "start": 1748230513169, "end": 1748230513174, "duration": 5, "pid": 24156, "index": 37}, {"name": "Require(32) node_modules/egg-i18n/app/extend/context.js", "start": 1748230513176, "end": 1748230513177, "duration": 1, "pid": 24156, "index": 38}, {"name": "Require(33) node_modules/egg-multipart/app/extend/context.js", "start": 1748230513179, "end": 1748230513207, "duration": 28, "pid": 24156, "index": 39}, {"name": "Require(34) node_modules/egg-view/app/extend/context.js", "start": 1748230513208, "end": 1748230513211, "duration": 3, "pid": 24156, "index": 40}, {"name": "Require(35) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748230513214, "end": 1748230513214, "duration": 0, "pid": 24156, "index": 41}, {"name": "Require(36) node_modules/egg/app/extend/context.js", "start": 1748230513216, "end": 1748230513219, "duration": 3, "pid": 24156, "index": 42}, {"name": "Load agent.js", "start": 1748230513226, "end": 1748230513294, "duration": 68, "pid": 24156, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1748230513227, "end": 1748230513228, "duration": 1, "pid": 24156, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1748230513229, "end": 1748230513229, "duration": 0, "pid": 24156, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1748230513230, "end": 1748230513244, "duration": 14, "pid": 24156, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1748230513245, "end": 1748230513247, "duration": 2, "pid": 24156, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1748230513249, "end": 1748230513269, "duration": 20, "pid": 24156, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1748230513270, "end": 1748230513270, "duration": 0, "pid": 24156, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1748230513272, "end": 1748230513272, "duration": 0, "pid": 24156, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1748230513274, "end": 1748230513288, "duration": 14, "pid": 24156, "index": 51}, {"name": "Require(45) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748230513288, "end": 1748230513293, "duration": 5, "pid": 24156, "index": 52}, {"name": "Require(46) node_modules/egg/agent.js", "start": 1748230513293, "end": 1748230513294, "duration": 1, "pid": 24156, "index": 53}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748230513301, "end": 1748230514143, "duration": 842, "pid": 24156, "index": 54}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748230513302, "end": 1748230514104, "duration": 802, "pid": 24156, "index": 55}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1748230513302, "end": 1748230514430, "duration": 1128, "pid": 24156, "index": 56}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748230513884, "end": 1748230513980, "duration": 96, "pid": 24156, "index": 57}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748230513911, "end": 1748230514139, "duration": 228, "pid": 24156, "index": 58}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748230514015, "end": 1748230514399, "duration": 384, "pid": 24156, "index": 59}]