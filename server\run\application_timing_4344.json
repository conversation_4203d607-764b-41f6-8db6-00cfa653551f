[{"name": "Process Start", "start": 1746258149276, "end": 1746258151093, "duration": 1817, "pid": 4344, "index": 0}, {"name": "Application Start", "start": 1746258151095, "end": 1746258153261, "duration": 2166, "pid": 4344, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746258151116, "end": 1746258151150, "duration": 34, "pid": 4344, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746258151150, "end": 1746258151204, "duration": 54, "pid": 4344, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746258151151, "end": 1746258151152, "duration": 1, "pid": 4344, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746258151154, "end": 1746258151155, "duration": 1, "pid": 4344, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746258151156, "end": 1746258151156, "duration": 0, "pid": 4344, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746258151157, "end": 1746258151158, "duration": 1, "pid": 4344, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746258151163, "end": 1746258151164, "duration": 1, "pid": 4344, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746258151165, "end": 1746258151165, "duration": 0, "pid": 4344, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746258151166, "end": 1746258151167, "duration": 1, "pid": 4344, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746258151167, "end": 1746258151168, "duration": 1, "pid": 4344, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746258151169, "end": 1746258151169, "duration": 0, "pid": 4344, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746258151170, "end": 1746258151171, "duration": 1, "pid": 4344, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746258151172, "end": 1746258151173, "duration": 1, "pid": 4344, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746258151173, "end": 1746258151174, "duration": 1, "pid": 4344, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746258151175, "end": 1746258151175, "duration": 0, "pid": 4344, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746258151176, "end": 1746258151177, "duration": 1, "pid": 4344, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746258151179, "end": 1746258151180, "duration": 1, "pid": 4344, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746258151181, "end": 1746258151182, "duration": 1, "pid": 4344, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746258151182, "end": 1746258151183, "duration": 1, "pid": 4344, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746258151184, "end": 1746258151184, "duration": 0, "pid": 4344, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746258151185, "end": 1746258151186, "duration": 1, "pid": 4344, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746258151187, "end": 1746258151187, "duration": 0, "pid": 4344, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746258151188, "end": 1746258151189, "duration": 1, "pid": 4344, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746258151190, "end": 1746258151190, "duration": 0, "pid": 4344, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746258151192, "end": 1746258151193, "duration": 1, "pid": 4344, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746258151195, "end": 1746258151196, "duration": 1, "pid": 4344, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746258151199, "end": 1746258151200, "duration": 1, "pid": 4344, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746258151203, "end": 1746258151204, "duration": 1, "pid": 4344, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746258151204, "end": 1746258151204, "duration": 0, "pid": 4344, "index": 30}, {"name": "Load extend/application.js", "start": 1746258151206, "end": 1746258151320, "duration": 114, "pid": 4344, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746258151207, "end": 1746258151208, "duration": 1, "pid": 4344, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746258151209, "end": 1746258151212, "duration": 3, "pid": 4344, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746258151213, "end": 1746258151219, "duration": 6, "pid": 4344, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746258151221, "end": 1746258151230, "duration": 9, "pid": 4344, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746258151231, "end": 1746258151233, "duration": 2, "pid": 4344, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746258151234, "end": 1746258151237, "duration": 3, "pid": 4344, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746258151238, "end": 1746258151309, "duration": 71, "pid": 4344, "index": 38}, {"name": "Load extend/request.js", "start": 1746258151321, "end": 1746258151339, "duration": 18, "pid": 4344, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746258151328, "end": 1746258151331, "duration": 3, "pid": 4344, "index": 40}, {"name": "Load extend/response.js", "start": 1746258151339, "end": 1746258151360, "duration": 21, "pid": 4344, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746258151349, "end": 1746258151352, "duration": 3, "pid": 4344, "index": 42}, {"name": "Load extend/context.js", "start": 1746258151360, "end": 1746258151446, "duration": 86, "pid": 4344, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746258151362, "end": 1746258151387, "duration": 25, "pid": 4344, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746258151388, "end": 1746258151391, "duration": 3, "pid": 4344, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746258151392, "end": 1746258151392, "duration": 0, "pid": 4344, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746258151394, "end": 1746258151424, "duration": 30, "pid": 4344, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746258151425, "end": 1746258151428, "duration": 3, "pid": 4344, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746258151431, "end": 1746258151431, "duration": 0, "pid": 4344, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746258151433, "end": 1746258151436, "duration": 3, "pid": 4344, "index": 50}, {"name": "Load extend/helper.js", "start": 1746258151446, "end": 1746258151489, "duration": 43, "pid": 4344, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746258151447, "end": 1746258151474, "duration": 27, "pid": 4344, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746258151480, "end": 1746258151481, "duration": 1, "pid": 4344, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746258151481, "end": 1746258151482, "duration": 1, "pid": 4344, "index": 54}, {"name": "Load app.js", "start": 1746258151489, "end": 1746258151589, "duration": 100, "pid": 4344, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746258151490, "end": 1746258151490, "duration": 0, "pid": 4344, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746258151491, "end": 1746258151494, "duration": 3, "pid": 4344, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746258151496, "end": 1746258151512, "duration": 16, "pid": 4344, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746258151513, "end": 1746258151529, "duration": 16, "pid": 4344, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746258151530, "end": 1746258151544, "duration": 14, "pid": 4344, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746258151545, "end": 1746258151546, "duration": 1, "pid": 4344, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746258151547, "end": 1746258151549, "duration": 2, "pid": 4344, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746258151550, "end": 1746258151550, "duration": 0, "pid": 4344, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746258151551, "end": 1746258151551, "duration": 0, "pid": 4344, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746258151552, "end": 1746258151552, "duration": 0, "pid": 4344, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746258151553, "end": 1746258151554, "duration": 1, "pid": 4344, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746258151554, "end": 1746258151554, "duration": 0, "pid": 4344, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746258151555, "end": 1746258151556, "duration": 1, "pid": 4344, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746258151556, "end": 1746258151562, "duration": 6, "pid": 4344, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746258151563, "end": 1746258151581, "duration": 18, "pid": 4344, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746258151582, "end": 1746258151587, "duration": 5, "pid": 4344, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746258151602, "end": 1746258153242, "duration": 1640, "pid": 4344, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746258152349, "end": 1746258152423, "duration": 74, "pid": 4344, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746258152375, "end": 1746258153166, "duration": 791, "pid": 4344, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746258152452, "end": 1746258153260, "duration": 808, "pid": 4344, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746258152569, "end": 1746258153245, "duration": 676, "pid": 4344, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746258152683, "end": 1746258153199, "duration": 516, "pid": 4344, "index": 77}, {"name": "Load Service", "start": 1746258152683, "end": 1746258152843, "duration": 160, "pid": 4344, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746258152683, "end": 1746258152843, "duration": 160, "pid": 4344, "index": 79}, {"name": "Load Middleware", "start": 1746258152843, "end": 1746258153038, "duration": 195, "pid": 4344, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746258152843, "end": 1746258153021, "duration": 178, "pid": 4344, "index": 81}, {"name": "Load Controller", "start": 1746258153038, "end": 1746258153084, "duration": 46, "pid": 4344, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746258153038, "end": 1746258153084, "duration": 46, "pid": 4344, "index": 83}, {"name": "Load Router", "start": 1746258153084, "end": 1746258153090, "duration": 6, "pid": 4344, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746258153084, "end": 1746258153085, "duration": 1, "pid": 4344, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746258153086, "end": 1746258153166, "duration": 80, "pid": 4344, "index": 86}]