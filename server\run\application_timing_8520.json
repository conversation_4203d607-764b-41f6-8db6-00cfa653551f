[{"name": "Process Start", "start": 1746258703642, "end": 1746258705520, "duration": 1878, "pid": 8520, "index": 0}, {"name": "Application Start", "start": 1746258705521, "end": 1746258708058, "duration": 2537, "pid": 8520, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746258705544, "end": 1746258705580, "duration": 36, "pid": 8520, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746258705580, "end": 1746258705627, "duration": 47, "pid": 8520, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746258705581, "end": 1746258705581, "duration": 0, "pid": 8520, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746258705583, "end": 1746258705583, "duration": 0, "pid": 8520, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746258705584, "end": 1746258705585, "duration": 1, "pid": 8520, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746258705585, "end": 1746258705586, "duration": 1, "pid": 8520, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746258705587, "end": 1746258705588, "duration": 1, "pid": 8520, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746258705588, "end": 1746258705589, "duration": 1, "pid": 8520, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746258705590, "end": 1746258705591, "duration": 1, "pid": 8520, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746258705592, "end": 1746258705592, "duration": 0, "pid": 8520, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746258705593, "end": 1746258705594, "duration": 1, "pid": 8520, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746258705597, "end": 1746258705598, "duration": 1, "pid": 8520, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746258705599, "end": 1746258705599, "duration": 0, "pid": 8520, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746258705600, "end": 1746258705601, "duration": 1, "pid": 8520, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746258705601, "end": 1746258705602, "duration": 1, "pid": 8520, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746258705603, "end": 1746258705603, "duration": 0, "pid": 8520, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746258705604, "end": 1746258705605, "duration": 1, "pid": 8520, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746258705606, "end": 1746258705607, "duration": 1, "pid": 8520, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746258705608, "end": 1746258705609, "duration": 1, "pid": 8520, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746258705610, "end": 1746258705611, "duration": 1, "pid": 8520, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746258705611, "end": 1746258705612, "duration": 1, "pid": 8520, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746258705612, "end": 1746258705613, "duration": 1, "pid": 8520, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746258705613, "end": 1746258705614, "duration": 1, "pid": 8520, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746258705615, "end": 1746258705615, "duration": 0, "pid": 8520, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746258705617, "end": 1746258705617, "duration": 0, "pid": 8520, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746258705619, "end": 1746258705619, "duration": 0, "pid": 8520, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746258705621, "end": 1746258705622, "duration": 1, "pid": 8520, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746258705626, "end": 1746258705627, "duration": 1, "pid": 8520, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746258705627, "end": 1746258705627, "duration": 0, "pid": 8520, "index": 30}, {"name": "Load extend/application.js", "start": 1746258705629, "end": 1746258705751, "duration": 122, "pid": 8520, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746258705630, "end": 1746258705631, "duration": 1, "pid": 8520, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746258705632, "end": 1746258705634, "duration": 2, "pid": 8520, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746258705635, "end": 1746258705642, "duration": 7, "pid": 8520, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746258705644, "end": 1746258705651, "duration": 7, "pid": 8520, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746258705652, "end": 1746258705654, "duration": 2, "pid": 8520, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746258705655, "end": 1746258705658, "duration": 3, "pid": 8520, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746258705659, "end": 1746258705739, "duration": 80, "pid": 8520, "index": 38}, {"name": "Load extend/request.js", "start": 1746258705752, "end": 1746258705771, "duration": 19, "pid": 8520, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746258705761, "end": 1746258705763, "duration": 2, "pid": 8520, "index": 40}, {"name": "Load extend/response.js", "start": 1746258705771, "end": 1746258705794, "duration": 23, "pid": 8520, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746258705781, "end": 1746258705785, "duration": 4, "pid": 8520, "index": 42}, {"name": "Load extend/context.js", "start": 1746258705794, "end": 1746258705874, "duration": 80, "pid": 8520, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746258705795, "end": 1746258705814, "duration": 19, "pid": 8520, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746258705815, "end": 1746258705817, "duration": 2, "pid": 8520, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746258705819, "end": 1746258705819, "duration": 0, "pid": 8520, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746258705821, "end": 1746258705855, "duration": 34, "pid": 8520, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746258705858, "end": 1746258705860, "duration": 2, "pid": 8520, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746258705862, "end": 1746258705862, "duration": 0, "pid": 8520, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746258705863, "end": 1746258705866, "duration": 3, "pid": 8520, "index": 50}, {"name": "Load extend/helper.js", "start": 1746258705875, "end": 1746258705923, "duration": 48, "pid": 8520, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746258705876, "end": 1746258705905, "duration": 29, "pid": 8520, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746258705912, "end": 1746258705913, "duration": 1, "pid": 8520, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746258705913, "end": 1746258705914, "duration": 1, "pid": 8520, "index": 54}, {"name": "Load app.js", "start": 1746258705924, "end": 1746258706029, "duration": 105, "pid": 8520, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746258705924, "end": 1746258705925, "duration": 1, "pid": 8520, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746258705926, "end": 1746258705930, "duration": 4, "pid": 8520, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746258705931, "end": 1746258705947, "duration": 16, "pid": 8520, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746258705948, "end": 1746258705968, "duration": 20, "pid": 8520, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746258705969, "end": 1746258705985, "duration": 16, "pid": 8520, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746258705985, "end": 1746258705986, "duration": 1, "pid": 8520, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746258705987, "end": 1746258705990, "duration": 3, "pid": 8520, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746258705991, "end": 1746258705992, "duration": 1, "pid": 8520, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746258705993, "end": 1746258705993, "duration": 0, "pid": 8520, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746258705994, "end": 1746258705994, "duration": 0, "pid": 8520, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746258705995, "end": 1746258705996, "duration": 1, "pid": 8520, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746258705996, "end": 1746258705997, "duration": 1, "pid": 8520, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746258705997, "end": 1746258705998, "duration": 1, "pid": 8520, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746258705998, "end": 1746258706001, "duration": 3, "pid": 8520, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746258706004, "end": 1746258706021, "duration": 17, "pid": 8520, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746258706021, "end": 1746258706028, "duration": 7, "pid": 8520, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746258706043, "end": 1746258708029, "duration": 1986, "pid": 8520, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746258706797, "end": 1746258706902, "duration": 105, "pid": 8520, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746258706828, "end": 1746258707897, "duration": 1069, "pid": 8520, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746258706966, "end": 1746258708055, "duration": 1089, "pid": 8520, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746258707147, "end": 1746258708026, "duration": 879, "pid": 8520, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746258707317, "end": 1746258707959, "duration": 642, "pid": 8520, "index": 77}, {"name": "Load Service", "start": 1746258707318, "end": 1746258707504, "duration": 186, "pid": 8520, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746258707318, "end": 1746258707504, "duration": 186, "pid": 8520, "index": 79}, {"name": "Load Middleware", "start": 1746258707505, "end": 1746258707731, "duration": 226, "pid": 8520, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746258707505, "end": 1746258707710, "duration": 205, "pid": 8520, "index": 81}, {"name": "Load Controller", "start": 1746258707731, "end": 1746258707783, "duration": 52, "pid": 8520, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746258707731, "end": 1746258707783, "duration": 52, "pid": 8520, "index": 83}, {"name": "Load Router", "start": 1746258707783, "end": 1746258707795, "duration": 12, "pid": 8520, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746258707784, "end": 1746258707785, "duration": 1, "pid": 8520, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746258707787, "end": 1746258707897, "duration": 110, "pid": 8520, "index": 86}]