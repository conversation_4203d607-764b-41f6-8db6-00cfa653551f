[{"name": "Process Start", "start": 1746258646488, "end": 1746258648761, "duration": 2273, "pid": 25864, "index": 0}, {"name": "Application Start", "start": 1746258648762, "end": 1746258651005, "duration": 2243, "pid": 25864, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746258648798, "end": 1746258648840, "duration": 42, "pid": 25864, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746258648840, "end": 1746258648888, "duration": 48, "pid": 25864, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746258648841, "end": 1746258648842, "duration": 1, "pid": 25864, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746258648844, "end": 1746258648844, "duration": 0, "pid": 25864, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746258648845, "end": 1746258648846, "duration": 1, "pid": 25864, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746258648847, "end": 1746258648847, "duration": 0, "pid": 25864, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746258648849, "end": 1746258648849, "duration": 0, "pid": 25864, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746258648850, "end": 1746258648851, "duration": 1, "pid": 25864, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746258648852, "end": 1746258648853, "duration": 1, "pid": 25864, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746258648854, "end": 1746258648855, "duration": 1, "pid": 25864, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746258648856, "end": 1746258648856, "duration": 0, "pid": 25864, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746258648858, "end": 1746258648858, "duration": 0, "pid": 25864, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746258648859, "end": 1746258648860, "duration": 1, "pid": 25864, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746258648861, "end": 1746258648862, "duration": 1, "pid": 25864, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746258648863, "end": 1746258648863, "duration": 0, "pid": 25864, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746258648864, "end": 1746258648864, "duration": 0, "pid": 25864, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746258648865, "end": 1746258648865, "duration": 0, "pid": 25864, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746258648866, "end": 1746258648867, "duration": 1, "pid": 25864, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746258648868, "end": 1746258648869, "duration": 1, "pid": 25864, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746258648869, "end": 1746258648870, "duration": 1, "pid": 25864, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746258648871, "end": 1746258648871, "duration": 0, "pid": 25864, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746258648872, "end": 1746258648872, "duration": 0, "pid": 25864, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746258648873, "end": 1746258648874, "duration": 1, "pid": 25864, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746258648876, "end": 1746258648876, "duration": 0, "pid": 25864, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746258648878, "end": 1746258648878, "duration": 0, "pid": 25864, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746258648880, "end": 1746258648881, "duration": 1, "pid": 25864, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746258648884, "end": 1746258648884, "duration": 0, "pid": 25864, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746258648887, "end": 1746258648887, "duration": 0, "pid": 25864, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746258648887, "end": 1746258648887, "duration": 0, "pid": 25864, "index": 30}, {"name": "Load extend/application.js", "start": 1746258648894, "end": 1746258648994, "duration": 100, "pid": 25864, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746258648896, "end": 1746258648896, "duration": 0, "pid": 25864, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746258648897, "end": 1746258648900, "duration": 3, "pid": 25864, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746258648901, "end": 1746258648910, "duration": 9, "pid": 25864, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746258648912, "end": 1746258648920, "duration": 8, "pid": 25864, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746258648922, "end": 1746258648924, "duration": 2, "pid": 25864, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746258648925, "end": 1746258648927, "duration": 2, "pid": 25864, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746258648928, "end": 1746258648985, "duration": 57, "pid": 25864, "index": 38}, {"name": "Load extend/request.js", "start": 1746258648994, "end": 1746258649013, "duration": 19, "pid": 25864, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746258649002, "end": 1746258649005, "duration": 3, "pid": 25864, "index": 40}, {"name": "Load extend/response.js", "start": 1746258649013, "end": 1746258649034, "duration": 21, "pid": 25864, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746258649022, "end": 1746258649026, "duration": 4, "pid": 25864, "index": 42}, {"name": "Load extend/context.js", "start": 1746258649034, "end": 1746258649122, "duration": 88, "pid": 25864, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746258649035, "end": 1746258649059, "duration": 24, "pid": 25864, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746258649059, "end": 1746258649062, "duration": 3, "pid": 25864, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746258649063, "end": 1746258649064, "duration": 1, "pid": 25864, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746258649065, "end": 1746258649098, "duration": 33, "pid": 25864, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746258649100, "end": 1746258649102, "duration": 2, "pid": 25864, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746258649105, "end": 1746258649106, "duration": 1, "pid": 25864, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746258649107, "end": 1746258649113, "duration": 6, "pid": 25864, "index": 50}, {"name": "Load extend/helper.js", "start": 1746258649122, "end": 1746258649176, "duration": 54, "pid": 25864, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746258649123, "end": 1746258649157, "duration": 34, "pid": 25864, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746258649164, "end": 1746258649165, "duration": 1, "pid": 25864, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746258649166, "end": 1746258649166, "duration": 0, "pid": 25864, "index": 54}, {"name": "Load app.js", "start": 1746258649177, "end": 1746258649284, "duration": 107, "pid": 25864, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746258649178, "end": 1746258649178, "duration": 0, "pid": 25864, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746258649180, "end": 1746258649185, "duration": 5, "pid": 25864, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746258649187, "end": 1746258649209, "duration": 22, "pid": 25864, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746258649210, "end": 1746258649226, "duration": 16, "pid": 25864, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746258649226, "end": 1746258649241, "duration": 15, "pid": 25864, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746258649242, "end": 1746258649243, "duration": 1, "pid": 25864, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746258649243, "end": 1746258649246, "duration": 3, "pid": 25864, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746258649246, "end": 1746258649247, "duration": 1, "pid": 25864, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746258649247, "end": 1746258649248, "duration": 1, "pid": 25864, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746258649248, "end": 1746258649249, "duration": 1, "pid": 25864, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746258649250, "end": 1746258649251, "duration": 1, "pid": 25864, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746258649251, "end": 1746258649252, "duration": 1, "pid": 25864, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746258649252, "end": 1746258649253, "duration": 1, "pid": 25864, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746258649253, "end": 1746258649256, "duration": 3, "pid": 25864, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746258649256, "end": 1746258649276, "duration": 20, "pid": 25864, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746258649276, "end": 1746258649282, "duration": 6, "pid": 25864, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746258649297, "end": 1746258650985, "duration": 1688, "pid": 25864, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746258650065, "end": 1746258650164, "duration": 99, "pid": 25864, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746258650095, "end": 1746258650928, "duration": 833, "pid": 25864, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746258650209, "end": 1746258651004, "duration": 795, "pid": 25864, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746258650335, "end": 1746258650987, "duration": 652, "pid": 25864, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746258650476, "end": 1746258650952, "duration": 476, "pid": 25864, "index": 77}, {"name": "Load Service", "start": 1746258650476, "end": 1746258650606, "duration": 130, "pid": 25864, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746258650476, "end": 1746258650606, "duration": 130, "pid": 25864, "index": 79}, {"name": "Load Middleware", "start": 1746258650606, "end": 1746258650807, "duration": 201, "pid": 25864, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746258650607, "end": 1746258650789, "duration": 182, "pid": 25864, "index": 81}, {"name": "Load Controller", "start": 1746258650808, "end": 1746258650854, "duration": 46, "pid": 25864, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746258650808, "end": 1746258650854, "duration": 46, "pid": 25864, "index": 83}, {"name": "Load Router", "start": 1746258650854, "end": 1746258650861, "duration": 7, "pid": 25864, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746258650855, "end": 1746258650856, "duration": 1, "pid": 25864, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746258650857, "end": 1746258650928, "duration": 71, "pid": 25864, "index": 86}]