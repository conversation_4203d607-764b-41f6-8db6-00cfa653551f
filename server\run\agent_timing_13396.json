[{"name": "Process Start", "start": 1746257613865, "end": 1746257622573, "duration": 8708, "pid": 13396, "index": 0}, {"name": "Application Start", "start": 1746257622576, "end": 1746257625154, "duration": 2578, "pid": 13396, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746257622606, "end": 1746257622663, "duration": 57, "pid": 13396, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746257622663, "end": 1746257622834, "duration": 171, "pid": 13396, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746257622664, "end": 1746257622665, "duration": 1, "pid": 13396, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746257622668, "end": 1746257622668, "duration": 0, "pid": 13396, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746257622670, "end": 1746257622671, "duration": 1, "pid": 13396, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746257622673, "end": 1746257622674, "duration": 1, "pid": 13396, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746257622677, "end": 1746257622678, "duration": 1, "pid": 13396, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746257622679, "end": 1746257622680, "duration": 1, "pid": 13396, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746257622681, "end": 1746257622682, "duration": 1, "pid": 13396, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746257622689, "end": 1746257622699, "duration": 10, "pid": 13396, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746257622703, "end": 1746257622705, "duration": 2, "pid": 13396, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746257622728, "end": 1746257622735, "duration": 7, "pid": 13396, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746257622765, "end": 1746257622788, "duration": 23, "pid": 13396, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746257622790, "end": 1746257622791, "duration": 1, "pid": 13396, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746257622795, "end": 1746257622796, "duration": 1, "pid": 13396, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746257622798, "end": 1746257622798, "duration": 0, "pid": 13396, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746257622800, "end": 1746257622800, "duration": 0, "pid": 13396, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746257622801, "end": 1746257622802, "duration": 1, "pid": 13396, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746257622803, "end": 1746257622803, "duration": 0, "pid": 13396, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746257622804, "end": 1746257622805, "duration": 1, "pid": 13396, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746257622806, "end": 1746257622807, "duration": 1, "pid": 13396, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746257622809, "end": 1746257622810, "duration": 1, "pid": 13396, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746257622811, "end": 1746257622812, "duration": 1, "pid": 13396, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746257622818, "end": 1746257622818, "duration": 0, "pid": 13396, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746257622820, "end": 1746257622821, "duration": 1, "pid": 13396, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746257622823, "end": 1746257622823, "duration": 0, "pid": 13396, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746257622828, "end": 1746257622829, "duration": 1, "pid": 13396, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746257622833, "end": 1746257622833, "duration": 0, "pid": 13396, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746257622833, "end": 1746257622833, "duration": 0, "pid": 13396, "index": 30}, {"name": "Load extend/agent.js", "start": 1746257622835, "end": 1746257623014, "duration": 179, "pid": 13396, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/agent.js", "start": 1746257622838, "end": 1746257622843, "duration": 5, "pid": 13396, "index": 32}, {"name": "Require(28) node_modules/egg-schedule/app/extend/agent.js", "start": 1746257622847, "end": 1746257622995, "duration": 148, "pid": 13396, "index": 33}, {"name": "Require(29) node_modules/egg-logrotator/app/extend/agent.js", "start": 1746257622997, "end": 1746257623000, "duration": 3, "pid": 13396, "index": 34}, {"name": "Load extend/context.js", "start": 1746257623014, "end": 1746257623135, "duration": 121, "pid": 13396, "index": 35}, {"name": "Require(30) node_modules/egg-security/app/extend/context.js", "start": 1746257623016, "end": 1746257623045, "duration": 29, "pid": 13396, "index": 36}, {"name": "Require(31) node_modules/egg-jsonp/app/extend/context.js", "start": 1746257623047, "end": 1746257623054, "duration": 7, "pid": 13396, "index": 37}, {"name": "Require(32) node_modules/egg-i18n/app/extend/context.js", "start": 1746257623055, "end": 1746257623056, "duration": 1, "pid": 13396, "index": 38}, {"name": "Require(33) node_modules/egg-multipart/app/extend/context.js", "start": 1746257623059, "end": 1746257623111, "duration": 52, "pid": 13396, "index": 39}, {"name": "Require(34) node_modules/egg-view/app/extend/context.js", "start": 1746257623113, "end": 1746257623117, "duration": 4, "pid": 13396, "index": 40}, {"name": "Require(35) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746257623120, "end": 1746257623120, "duration": 0, "pid": 13396, "index": 41}, {"name": "Require(36) node_modules/egg/app/extend/context.js", "start": 1746257623122, "end": 1746257623128, "duration": 6, "pid": 13396, "index": 42}, {"name": "Load agent.js", "start": 1746257623135, "end": 1746257623246, "duration": 111, "pid": 13396, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1746257623136, "end": 1746257623138, "duration": 2, "pid": 13396, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1746257623139, "end": 1746257623140, "duration": 1, "pid": 13396, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1746257623141, "end": 1746257623163, "duration": 22, "pid": 13396, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1746257623164, "end": 1746257623167, "duration": 3, "pid": 13396, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1746257623169, "end": 1746257623196, "duration": 27, "pid": 13396, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1746257623196, "end": 1746257623197, "duration": 1, "pid": 13396, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1746257623198, "end": 1746257623199, "duration": 1, "pid": 13396, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1746257623201, "end": 1746257623230, "duration": 29, "pid": 13396, "index": 51}, {"name": "Require(45) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1746257623231, "end": 1746257623243, "duration": 12, "pid": 13396, "index": 52}, {"name": "Require(46) node_modules/egg/agent.js", "start": 1746257623244, "end": 1746257623245, "duration": 1, "pid": 13396, "index": 53}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746257623255, "end": 1746257624765, "duration": 1510, "pid": 13396, "index": 54}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1746257623256, "end": 1746257624704, "duration": 1448, "pid": 13396, "index": 55}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1746257623256, "end": 1746257625153, "duration": 1897, "pid": 13396, "index": 56}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746257624238, "end": 1746257624392, "duration": 154, "pid": 13396, "index": 57}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746257624306, "end": 1746257624759, "duration": 453, "pid": 13396, "index": 58}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746257624442, "end": 1746257625133, "duration": 691, "pid": 13396, "index": 59}]