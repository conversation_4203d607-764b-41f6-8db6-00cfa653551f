[{"name": "Process Start", "start": 1746258723264, "end": 1746258725371, "duration": 2107, "pid": 24736, "index": 0}, {"name": "Application Start", "start": 1746258725374, "end": 1746258727622, "duration": 2248, "pid": 24736, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746258725406, "end": 1746258725453, "duration": 47, "pid": 24736, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746258725453, "end": 1746258725500, "duration": 47, "pid": 24736, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746258725455, "end": 1746258725456, "duration": 1, "pid": 24736, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746258725458, "end": 1746258725459, "duration": 1, "pid": 24736, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746258725460, "end": 1746258725460, "duration": 0, "pid": 24736, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746258725461, "end": 1746258725462, "duration": 1, "pid": 24736, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746258725463, "end": 1746258725463, "duration": 0, "pid": 24736, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746258725464, "end": 1746258725465, "duration": 1, "pid": 24736, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746258725465, "end": 1746258725466, "duration": 1, "pid": 24736, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746258725466, "end": 1746258725467, "duration": 1, "pid": 24736, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746258725468, "end": 1746258725468, "duration": 0, "pid": 24736, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746258725470, "end": 1746258725471, "duration": 1, "pid": 24736, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746258725473, "end": 1746258725473, "duration": 0, "pid": 24736, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746258725474, "end": 1746258725475, "duration": 1, "pid": 24736, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746258725475, "end": 1746258725476, "duration": 1, "pid": 24736, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746258725477, "end": 1746258725477, "duration": 0, "pid": 24736, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746258725478, "end": 1746258725478, "duration": 0, "pid": 24736, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746258725479, "end": 1746258725480, "duration": 1, "pid": 24736, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746258725481, "end": 1746258725481, "duration": 0, "pid": 24736, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746258725482, "end": 1746258725482, "duration": 0, "pid": 24736, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746258725483, "end": 1746258725483, "duration": 0, "pid": 24736, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746258725484, "end": 1746258725485, "duration": 1, "pid": 24736, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746258725485, "end": 1746258725487, "duration": 2, "pid": 24736, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746258725489, "end": 1746258725489, "duration": 0, "pid": 24736, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746258725490, "end": 1746258725491, "duration": 1, "pid": 24736, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746258725492, "end": 1746258725493, "duration": 1, "pid": 24736, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746258725495, "end": 1746258725496, "duration": 1, "pid": 24736, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746258725499, "end": 1746258725500, "duration": 1, "pid": 24736, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746258725500, "end": 1746258725500, "duration": 0, "pid": 24736, "index": 30}, {"name": "Load extend/application.js", "start": 1746258725501, "end": 1746258725617, "duration": 116, "pid": 24736, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746258725503, "end": 1746258725504, "duration": 1, "pid": 24736, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746258725505, "end": 1746258725507, "duration": 2, "pid": 24736, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746258725508, "end": 1746258725513, "duration": 5, "pid": 24736, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746258725515, "end": 1746258725524, "duration": 9, "pid": 24736, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746258725525, "end": 1746258725527, "duration": 2, "pid": 24736, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746258725528, "end": 1746258725531, "duration": 3, "pid": 24736, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746258725532, "end": 1746258725603, "duration": 71, "pid": 24736, "index": 38}, {"name": "Load extend/request.js", "start": 1746258725617, "end": 1746258725638, "duration": 21, "pid": 24736, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746258725628, "end": 1746258725630, "duration": 2, "pid": 24736, "index": 40}, {"name": "Load extend/response.js", "start": 1746258725638, "end": 1746258725663, "duration": 25, "pid": 24736, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746258725648, "end": 1746258725653, "duration": 5, "pid": 24736, "index": 42}, {"name": "Load extend/context.js", "start": 1746258725663, "end": 1746258725748, "duration": 85, "pid": 24736, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746258725664, "end": 1746258725687, "duration": 23, "pid": 24736, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746258725688, "end": 1746258725691, "duration": 3, "pid": 24736, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746258725692, "end": 1746258725693, "duration": 1, "pid": 24736, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746258725694, "end": 1746258725729, "duration": 35, "pid": 24736, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746258725731, "end": 1746258725732, "duration": 1, "pid": 24736, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746258725734, "end": 1746258725735, "duration": 1, "pid": 24736, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746258725737, "end": 1746258725741, "duration": 4, "pid": 24736, "index": 50}, {"name": "Load extend/helper.js", "start": 1746258725749, "end": 1746258725797, "duration": 48, "pid": 24736, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746258725750, "end": 1746258725780, "duration": 30, "pid": 24736, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746258725787, "end": 1746258725788, "duration": 1, "pid": 24736, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746258725788, "end": 1746258725789, "duration": 1, "pid": 24736, "index": 54}, {"name": "Load app.js", "start": 1746258725797, "end": 1746258725893, "duration": 96, "pid": 24736, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746258725798, "end": 1746258725799, "duration": 1, "pid": 24736, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746258725800, "end": 1746258725803, "duration": 3, "pid": 24736, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746258725804, "end": 1746258725820, "duration": 16, "pid": 24736, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746258725820, "end": 1746258725839, "duration": 19, "pid": 24736, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746258725839, "end": 1746258725854, "duration": 15, "pid": 24736, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746258725855, "end": 1746258725856, "duration": 1, "pid": 24736, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746258725856, "end": 1746258725858, "duration": 2, "pid": 24736, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746258725859, "end": 1746258725860, "duration": 1, "pid": 24736, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746258725860, "end": 1746258725861, "duration": 1, "pid": 24736, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746258725861, "end": 1746258725861, "duration": 0, "pid": 24736, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746258725863, "end": 1746258725863, "duration": 0, "pid": 24736, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746258725863, "end": 1746258725864, "duration": 1, "pid": 24736, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746258725864, "end": 1746258725865, "duration": 1, "pid": 24736, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746258725865, "end": 1746258725867, "duration": 2, "pid": 24736, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746258725871, "end": 1746258725885, "duration": 14, "pid": 24736, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746258725886, "end": 1746258725892, "duration": 6, "pid": 24736, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746258725905, "end": 1746258727599, "duration": 1694, "pid": 24736, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746258726726, "end": 1746258726804, "duration": 78, "pid": 24736, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746258726749, "end": 1746258727525, "duration": 776, "pid": 24736, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746258726842, "end": 1746258727621, "duration": 779, "pid": 24736, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746258726964, "end": 1746258727601, "duration": 637, "pid": 24736, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746258727064, "end": 1746258727559, "duration": 495, "pid": 24736, "index": 77}, {"name": "Load Service", "start": 1746258727065, "end": 1746258727216, "duration": 151, "pid": 24736, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746258727065, "end": 1746258727216, "duration": 151, "pid": 24736, "index": 79}, {"name": "Load Middleware", "start": 1746258727216, "end": 1746258727382, "duration": 166, "pid": 24736, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746258727216, "end": 1746258727366, "duration": 150, "pid": 24736, "index": 81}, {"name": "Load Controller", "start": 1746258727383, "end": 1746258727442, "duration": 59, "pid": 24736, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746258727383, "end": 1746258727442, "duration": 59, "pid": 24736, "index": 83}, {"name": "Load Router", "start": 1746258727442, "end": 1746258727450, "duration": 8, "pid": 24736, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746258727443, "end": 1746258727444, "duration": 1, "pid": 24736, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746258727445, "end": 1746258727525, "duration": 80, "pid": 24736, "index": 86}]