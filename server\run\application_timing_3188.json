[{"name": "Process Start", "start": 1746257625358, "end": 1746257628177, "duration": 2819, "pid": 3188, "index": 0}, {"name": "Application Start", "start": 1746257628179, "end": 1746257633400, "duration": 5221, "pid": 3188, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746257628206, "end": 1746257628249, "duration": 43, "pid": 3188, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746257628249, "end": 1746257628299, "duration": 50, "pid": 3188, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746257628251, "end": 1746257628251, "duration": 0, "pid": 3188, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746257628253, "end": 1746257628254, "duration": 1, "pid": 3188, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746257628255, "end": 1746257628255, "duration": 0, "pid": 3188, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746257628256, "end": 1746257628256, "duration": 0, "pid": 3188, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746257628260, "end": 1746257628261, "duration": 1, "pid": 3188, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746257628262, "end": 1746257628263, "duration": 1, "pid": 3188, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746257628263, "end": 1746257628264, "duration": 1, "pid": 3188, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746257628265, "end": 1746257628265, "duration": 0, "pid": 3188, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746257628266, "end": 1746257628267, "duration": 1, "pid": 3188, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746257628267, "end": 1746257628268, "duration": 1, "pid": 3188, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746257628269, "end": 1746257628269, "duration": 0, "pid": 3188, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746257628270, "end": 1746257628271, "duration": 1, "pid": 3188, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746257628271, "end": 1746257628272, "duration": 1, "pid": 3188, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746257628273, "end": 1746257628273, "duration": 0, "pid": 3188, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746257628274, "end": 1746257628275, "duration": 1, "pid": 3188, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746257628276, "end": 1746257628276, "duration": 0, "pid": 3188, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746257628277, "end": 1746257628278, "duration": 1, "pid": 3188, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746257628278, "end": 1746257628279, "duration": 1, "pid": 3188, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746257628280, "end": 1746257628280, "duration": 0, "pid": 3188, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746257628281, "end": 1746257628282, "duration": 1, "pid": 3188, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746257628283, "end": 1746257628284, "duration": 1, "pid": 3188, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746257628286, "end": 1746257628286, "duration": 0, "pid": 3188, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746257628288, "end": 1746257628288, "duration": 0, "pid": 3188, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746257628290, "end": 1746257628291, "duration": 1, "pid": 3188, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746257628294, "end": 1746257628295, "duration": 1, "pid": 3188, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746257628298, "end": 1746257628299, "duration": 1, "pid": 3188, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746257628299, "end": 1746257628299, "duration": 0, "pid": 3188, "index": 30}, {"name": "Load extend/application.js", "start": 1746257628300, "end": 1746257628457, "duration": 157, "pid": 3188, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746257628301, "end": 1746257628312, "duration": 11, "pid": 3188, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746257628313, "end": 1746257628316, "duration": 3, "pid": 3188, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746257628317, "end": 1746257628325, "duration": 8, "pid": 3188, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746257628328, "end": 1746257628336, "duration": 8, "pid": 3188, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746257628338, "end": 1746257628341, "duration": 3, "pid": 3188, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746257628344, "end": 1746257628348, "duration": 4, "pid": 3188, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746257628350, "end": 1746257628446, "duration": 96, "pid": 3188, "index": 38}, {"name": "Load extend/request.js", "start": 1746257628457, "end": 1746257628477, "duration": 20, "pid": 3188, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746257628465, "end": 1746257628468, "duration": 3, "pid": 3188, "index": 40}, {"name": "Load extend/response.js", "start": 1746257628477, "end": 1746257628499, "duration": 22, "pid": 3188, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746257628486, "end": 1746257628491, "duration": 5, "pid": 3188, "index": 42}, {"name": "Load extend/context.js", "start": 1746257628499, "end": 1746257628585, "duration": 86, "pid": 3188, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746257628500, "end": 1746257628523, "duration": 23, "pid": 3188, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746257628523, "end": 1746257628527, "duration": 4, "pid": 3188, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746257628529, "end": 1746257628529, "duration": 0, "pid": 3188, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746257628531, "end": 1746257628563, "duration": 32, "pid": 3188, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746257628565, "end": 1746257628566, "duration": 1, "pid": 3188, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746257628568, "end": 1746257628569, "duration": 1, "pid": 3188, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746257628570, "end": 1746257628573, "duration": 3, "pid": 3188, "index": 50}, {"name": "Load extend/helper.js", "start": 1746257628585, "end": 1746257628660, "duration": 75, "pid": 3188, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746257628587, "end": 1746257628637, "duration": 50, "pid": 3188, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746257628644, "end": 1746257628645, "duration": 1, "pid": 3188, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746257628646, "end": 1746257628647, "duration": 1, "pid": 3188, "index": 54}, {"name": "Load app.js", "start": 1746257628660, "end": 1746257628845, "duration": 185, "pid": 3188, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746257628661, "end": 1746257628662, "duration": 1, "pid": 3188, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746257628663, "end": 1746257628668, "duration": 5, "pid": 3188, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746257628669, "end": 1746257628696, "duration": 27, "pid": 3188, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746257628697, "end": 1746257628728, "duration": 31, "pid": 3188, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746257628729, "end": 1746257628750, "duration": 21, "pid": 3188, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746257628751, "end": 1746257628753, "duration": 2, "pid": 3188, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746257628754, "end": 1746257628757, "duration": 3, "pid": 3188, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746257628758, "end": 1746257628759, "duration": 1, "pid": 3188, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746257628761, "end": 1746257628761, "duration": 0, "pid": 3188, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746257628762, "end": 1746257628763, "duration": 1, "pid": 3188, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746257628764, "end": 1746257628765, "duration": 1, "pid": 3188, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746257628766, "end": 1746257628767, "duration": 1, "pid": 3188, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746257628768, "end": 1746257628769, "duration": 1, "pid": 3188, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746257628770, "end": 1746257628783, "duration": 13, "pid": 3188, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746257628784, "end": 1746257628833, "duration": 49, "pid": 3188, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746257628834, "end": 1746257628842, "duration": 8, "pid": 3188, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746257628869, "end": 1746257633399, "duration": 4530, "pid": 3188, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746257629974, "end": 1746257630199, "duration": 225, "pid": 3188, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746257630020, "end": 1746257632000, "duration": 1980, "pid": 3188, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746257630289, "end": 1746257632437, "duration": 2148, "pid": 3188, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746257630521, "end": 1746257632342, "duration": 1821, "pid": 3188, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746257630686, "end": 1746257632140, "duration": 1454, "pid": 3188, "index": 77}, {"name": "Load Service", "start": 1746257630686, "end": 1746257630955, "duration": 269, "pid": 3188, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746257630686, "end": 1746257630955, "duration": 269, "pid": 3188, "index": 79}, {"name": "Load Middleware", "start": 1746257630955, "end": 1746257631430, "duration": 475, "pid": 3188, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746257630955, "end": 1746257631355, "duration": 400, "pid": 3188, "index": 81}, {"name": "Load Controller", "start": 1746257631430, "end": 1746257631623, "duration": 193, "pid": 3188, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746257631430, "end": 1746257631623, "duration": 193, "pid": 3188, "index": 83}, {"name": "Load Router", "start": 1746257631623, "end": 1746257631649, "duration": 26, "pid": 3188, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746257631623, "end": 1746257631628, "duration": 5, "pid": 3188, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746257631630, "end": 1746257632000, "duration": 370, "pid": 3188, "index": 86}]