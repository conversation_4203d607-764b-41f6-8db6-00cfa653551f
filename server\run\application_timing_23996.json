[{"name": "Process Start", "start": 1746258611800, "end": 1746258613519, "duration": 1719, "pid": 23996, "index": 0}, {"name": "Application Start", "start": 1746258613521, "end": 1746258616011, "duration": 2490, "pid": 23996, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746258613542, "end": 1746258613580, "duration": 38, "pid": 23996, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746258613581, "end": 1746258613637, "duration": 56, "pid": 23996, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746258613582, "end": 1746258613583, "duration": 1, "pid": 23996, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746258613585, "end": 1746258613586, "duration": 1, "pid": 23996, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746258613587, "end": 1746258613588, "duration": 1, "pid": 23996, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746258613589, "end": 1746258613590, "duration": 1, "pid": 23996, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746258613591, "end": 1746258613592, "duration": 1, "pid": 23996, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746258613593, "end": 1746258613593, "duration": 0, "pid": 23996, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746258613597, "end": 1746258613598, "duration": 1, "pid": 23996, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746258613599, "end": 1746258613599, "duration": 0, "pid": 23996, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746258613600, "end": 1746258613601, "duration": 1, "pid": 23996, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746258613602, "end": 1746258613603, "duration": 1, "pid": 23996, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746258613604, "end": 1746258613605, "duration": 1, "pid": 23996, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746258613606, "end": 1746258613607, "duration": 1, "pid": 23996, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746258613608, "end": 1746258613608, "duration": 0, "pid": 23996, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746258613609, "end": 1746258613610, "duration": 1, "pid": 23996, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746258613611, "end": 1746258613612, "duration": 1, "pid": 23996, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746258613613, "end": 1746258613614, "duration": 1, "pid": 23996, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746258613615, "end": 1746258613615, "duration": 0, "pid": 23996, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746258613616, "end": 1746258613617, "duration": 1, "pid": 23996, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746258613618, "end": 1746258613618, "duration": 0, "pid": 23996, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746258613619, "end": 1746258613620, "duration": 1, "pid": 23996, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746258613621, "end": 1746258613622, "duration": 1, "pid": 23996, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746258613624, "end": 1746258613624, "duration": 0, "pid": 23996, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746258613625, "end": 1746258613626, "duration": 1, "pid": 23996, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746258613627, "end": 1746258613628, "duration": 1, "pid": 23996, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746258613632, "end": 1746258613632, "duration": 0, "pid": 23996, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746258613637, "end": 1746258613637, "duration": 0, "pid": 23996, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746258613637, "end": 1746258613637, "duration": 0, "pid": 23996, "index": 30}, {"name": "Load extend/application.js", "start": 1746258613639, "end": 1746258613756, "duration": 117, "pid": 23996, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746258613640, "end": 1746258613641, "duration": 1, "pid": 23996, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746258613642, "end": 1746258613644, "duration": 2, "pid": 23996, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746258613645, "end": 1746258613652, "duration": 7, "pid": 23996, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746258613654, "end": 1746258613666, "duration": 12, "pid": 23996, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746258613668, "end": 1746258613671, "duration": 3, "pid": 23996, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746258613672, "end": 1746258613675, "duration": 3, "pid": 23996, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746258613676, "end": 1746258613745, "duration": 69, "pid": 23996, "index": 38}, {"name": "Load extend/request.js", "start": 1746258613756, "end": 1746258613773, "duration": 17, "pid": 23996, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746258613763, "end": 1746258613764, "duration": 1, "pid": 23996, "index": 40}, {"name": "Load extend/response.js", "start": 1746258613774, "end": 1746258613798, "duration": 24, "pid": 23996, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746258613783, "end": 1746258613788, "duration": 5, "pid": 23996, "index": 42}, {"name": "Load extend/context.js", "start": 1746258613798, "end": 1746258613901, "duration": 103, "pid": 23996, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746258613799, "end": 1746258613822, "duration": 23, "pid": 23996, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746258613822, "end": 1746258613825, "duration": 3, "pid": 23996, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746258613826, "end": 1746258613826, "duration": 0, "pid": 23996, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746258613827, "end": 1746258613875, "duration": 48, "pid": 23996, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746258613878, "end": 1746258613882, "duration": 4, "pid": 23996, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746258613885, "end": 1746258613885, "duration": 0, "pid": 23996, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746258613887, "end": 1746258613892, "duration": 5, "pid": 23996, "index": 50}, {"name": "Load extend/helper.js", "start": 1746258613901, "end": 1746258613954, "duration": 53, "pid": 23996, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746258613902, "end": 1746258613933, "duration": 31, "pid": 23996, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746258613941, "end": 1746258613942, "duration": 1, "pid": 23996, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746258613943, "end": 1746258613944, "duration": 1, "pid": 23996, "index": 54}, {"name": "Load app.js", "start": 1746258613954, "end": 1746258614081, "duration": 127, "pid": 23996, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746258613955, "end": 1746258613955, "duration": 0, "pid": 23996, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746258613956, "end": 1746258613960, "duration": 4, "pid": 23996, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746258613961, "end": 1746258613978, "duration": 17, "pid": 23996, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746258613979, "end": 1746258613995, "duration": 16, "pid": 23996, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746258613996, "end": 1746258614018, "duration": 22, "pid": 23996, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746258614019, "end": 1746258614021, "duration": 2, "pid": 23996, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746258614022, "end": 1746258614026, "duration": 4, "pid": 23996, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746258614026, "end": 1746258614028, "duration": 2, "pid": 23996, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746258614028, "end": 1746258614029, "duration": 1, "pid": 23996, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746258614030, "end": 1746258614030, "duration": 0, "pid": 23996, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746258614032, "end": 1746258614033, "duration": 1, "pid": 23996, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746258614034, "end": 1746258614035, "duration": 1, "pid": 23996, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746258614035, "end": 1746258614036, "duration": 1, "pid": 23996, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746258614037, "end": 1746258614042, "duration": 5, "pid": 23996, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746258614042, "end": 1746258614068, "duration": 26, "pid": 23996, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746258614069, "end": 1746258614078, "duration": 9, "pid": 23996, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746258614112, "end": 1746258615987, "duration": 1875, "pid": 23996, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746258615062, "end": 1746258615181, "duration": 119, "pid": 23996, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746258615099, "end": 1746258615907, "duration": 808, "pid": 23996, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746258615228, "end": 1746258616010, "duration": 782, "pid": 23996, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746258615337, "end": 1746258615990, "duration": 653, "pid": 23996, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746258615446, "end": 1746258615934, "duration": 488, "pid": 23996, "index": 77}, {"name": "Load Service", "start": 1746258615447, "end": 1746258615581, "duration": 134, "pid": 23996, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746258615447, "end": 1746258615581, "duration": 134, "pid": 23996, "index": 79}, {"name": "Load Middleware", "start": 1746258615581, "end": 1746258615773, "duration": 192, "pid": 23996, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746258615581, "end": 1746258615756, "duration": 175, "pid": 23996, "index": 81}, {"name": "Load Controller", "start": 1746258615773, "end": 1746258615819, "duration": 46, "pid": 23996, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746258615773, "end": 1746258615819, "duration": 46, "pid": 23996, "index": 83}, {"name": "Load Router", "start": 1746258615819, "end": 1746258615827, "duration": 8, "pid": 23996, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746258615819, "end": 1746258615821, "duration": 2, "pid": 23996, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746258615822, "end": 1746258615907, "duration": 85, "pid": 23996, "index": 86}]