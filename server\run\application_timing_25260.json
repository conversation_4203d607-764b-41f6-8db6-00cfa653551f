[{"name": "Process Start", "start": 1746258486546, "end": 1746258488635, "duration": 2089, "pid": 25260, "index": 0}, {"name": "Application Start", "start": 1746258488636, "end": 1746258490659, "duration": 2023, "pid": 25260, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746258488658, "end": 1746258488697, "duration": 39, "pid": 25260, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746258488697, "end": 1746258488742, "duration": 45, "pid": 25260, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746258488699, "end": 1746258488700, "duration": 1, "pid": 25260, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746258488702, "end": 1746258488702, "duration": 0, "pid": 25260, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746258488703, "end": 1746258488704, "duration": 1, "pid": 25260, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746258488704, "end": 1746258488705, "duration": 1, "pid": 25260, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746258488706, "end": 1746258488707, "duration": 1, "pid": 25260, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746258488708, "end": 1746258488708, "duration": 0, "pid": 25260, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746258488709, "end": 1746258488709, "duration": 0, "pid": 25260, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746258488710, "end": 1746258488710, "duration": 0, "pid": 25260, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746258488711, "end": 1746258488712, "duration": 1, "pid": 25260, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746258488713, "end": 1746258488713, "duration": 0, "pid": 25260, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746258488715, "end": 1746258488715, "duration": 0, "pid": 25260, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746258488716, "end": 1746258488717, "duration": 1, "pid": 25260, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746258488717, "end": 1746258488718, "duration": 1, "pid": 25260, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746258488719, "end": 1746258488720, "duration": 1, "pid": 25260, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746258488721, "end": 1746258488721, "duration": 0, "pid": 25260, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746258488722, "end": 1746258488723, "duration": 1, "pid": 25260, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746258488723, "end": 1746258488724, "duration": 1, "pid": 25260, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746258488724, "end": 1746258488725, "duration": 1, "pid": 25260, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746258488725, "end": 1746258488726, "duration": 1, "pid": 25260, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746258488727, "end": 1746258488727, "duration": 0, "pid": 25260, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746258488728, "end": 1746258488728, "duration": 0, "pid": 25260, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746258488729, "end": 1746258488729, "duration": 0, "pid": 25260, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746258488731, "end": 1746258488732, "duration": 1, "pid": 25260, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746258488735, "end": 1746258488735, "duration": 0, "pid": 25260, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746258488737, "end": 1746258488738, "duration": 1, "pid": 25260, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746258488741, "end": 1746258488741, "duration": 0, "pid": 25260, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746258488742, "end": 1746258488742, "duration": 0, "pid": 25260, "index": 30}, {"name": "Load extend/application.js", "start": 1746258488743, "end": 1746258488847, "duration": 104, "pid": 25260, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746258488744, "end": 1746258488744, "duration": 0, "pid": 25260, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746258488745, "end": 1746258488747, "duration": 2, "pid": 25260, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746258488748, "end": 1746258488755, "duration": 7, "pid": 25260, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746258488756, "end": 1746258488764, "duration": 8, "pid": 25260, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746258488766, "end": 1746258488769, "duration": 3, "pid": 25260, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746258488770, "end": 1746258488772, "duration": 2, "pid": 25260, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746258488773, "end": 1746258488838, "duration": 65, "pid": 25260, "index": 38}, {"name": "Load extend/request.js", "start": 1746258488847, "end": 1746258488863, "duration": 16, "pid": 25260, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746258488854, "end": 1746258488856, "duration": 2, "pid": 25260, "index": 40}, {"name": "Load extend/response.js", "start": 1746258488863, "end": 1746258488882, "duration": 19, "pid": 25260, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746258488871, "end": 1746258488874, "duration": 3, "pid": 25260, "index": 42}, {"name": "Load extend/context.js", "start": 1746258488882, "end": 1746258488951, "duration": 69, "pid": 25260, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746258488883, "end": 1746258488901, "duration": 18, "pid": 25260, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746258488901, "end": 1746258488904, "duration": 3, "pid": 25260, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746258488905, "end": 1746258488905, "duration": 0, "pid": 25260, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746258488907, "end": 1746258488935, "duration": 28, "pid": 25260, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746258488936, "end": 1746258488938, "duration": 2, "pid": 25260, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746258488939, "end": 1746258488940, "duration": 1, "pid": 25260, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746258488941, "end": 1746258488944, "duration": 3, "pid": 25260, "index": 50}, {"name": "Load extend/helper.js", "start": 1746258488951, "end": 1746258488991, "duration": 40, "pid": 25260, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746258488952, "end": 1746258488976, "duration": 24, "pid": 25260, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746258488982, "end": 1746258488983, "duration": 1, "pid": 25260, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746258488983, "end": 1746258488984, "duration": 1, "pid": 25260, "index": 54}, {"name": "Load app.js", "start": 1746258488991, "end": 1746258489082, "duration": 91, "pid": 25260, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746258488992, "end": 1746258488992, "duration": 0, "pid": 25260, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746258488993, "end": 1746258488995, "duration": 2, "pid": 25260, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746258488996, "end": 1746258489012, "duration": 16, "pid": 25260, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746258489013, "end": 1746258489027, "duration": 14, "pid": 25260, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746258489028, "end": 1746258489042, "duration": 14, "pid": 25260, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746258489043, "end": 1746258489044, "duration": 1, "pid": 25260, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746258489044, "end": 1746258489046, "duration": 2, "pid": 25260, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746258489047, "end": 1746258489048, "duration": 1, "pid": 25260, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746258489048, "end": 1746258489049, "duration": 1, "pid": 25260, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746258489050, "end": 1746258489050, "duration": 0, "pid": 25260, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746258489051, "end": 1746258489051, "duration": 0, "pid": 25260, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746258489052, "end": 1746258489052, "duration": 0, "pid": 25260, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746258489053, "end": 1746258489053, "duration": 0, "pid": 25260, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746258489054, "end": 1746258489056, "duration": 2, "pid": 25260, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746258489059, "end": 1746258489075, "duration": 16, "pid": 25260, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746258489075, "end": 1746258489080, "duration": 5, "pid": 25260, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746258489095, "end": 1746258490642, "duration": 1547, "pid": 25260, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746258489812, "end": 1746258489903, "duration": 91, "pid": 25260, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746258489838, "end": 1746258490587, "duration": 749, "pid": 25260, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746258489938, "end": 1746258490659, "duration": 721, "pid": 25260, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746258490033, "end": 1746258490640, "duration": 607, "pid": 25260, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746258490138, "end": 1746258490608, "duration": 470, "pid": 25260, "index": 77}, {"name": "Load Service", "start": 1746258490138, "end": 1746258490270, "duration": 132, "pid": 25260, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746258490138, "end": 1746258490270, "duration": 132, "pid": 25260, "index": 79}, {"name": "Load Middleware", "start": 1746258490271, "end": 1746258490455, "duration": 184, "pid": 25260, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746258490271, "end": 1746258490437, "duration": 166, "pid": 25260, "index": 81}, {"name": "Load Controller", "start": 1746258490455, "end": 1746258490500, "duration": 45, "pid": 25260, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746258490455, "end": 1746258490500, "duration": 45, "pid": 25260, "index": 83}, {"name": "Load Router", "start": 1746258490500, "end": 1746258490508, "duration": 8, "pid": 25260, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746258490501, "end": 1746258490502, "duration": 1, "pid": 25260, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746258490503, "end": 1746258490587, "duration": 84, "pid": 25260, "index": 86}]