[{"name": "Process Start", "start": 1746262519932, "end": 1746262521501, "duration": 1569, "pid": 24392, "index": 0}, {"name": "Application Start", "start": 1746262521502, "end": 1746262523273, "duration": 1771, "pid": 24392, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746262521521, "end": 1746262521554, "duration": 33, "pid": 24392, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746262521554, "end": 1746262521594, "duration": 40, "pid": 24392, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746262521555, "end": 1746262521555, "duration": 0, "pid": 24392, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746262521557, "end": 1746262521557, "duration": 0, "pid": 24392, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746262521558, "end": 1746262521559, "duration": 1, "pid": 24392, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746262521559, "end": 1746262521560, "duration": 1, "pid": 24392, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746262521561, "end": 1746262521562, "duration": 1, "pid": 24392, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746262521563, "end": 1746262521563, "duration": 0, "pid": 24392, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746262521564, "end": 1746262521565, "duration": 1, "pid": 24392, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746262521566, "end": 1746262521566, "duration": 0, "pid": 24392, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746262521567, "end": 1746262521567, "duration": 0, "pid": 24392, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746262521568, "end": 1746262521568, "duration": 0, "pid": 24392, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746262521569, "end": 1746262521570, "duration": 1, "pid": 24392, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746262521570, "end": 1746262521573, "duration": 3, "pid": 24392, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746262521573, "end": 1746262521574, "duration": 1, "pid": 24392, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746262521575, "end": 1746262521575, "duration": 0, "pid": 24392, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746262521576, "end": 1746262521577, "duration": 1, "pid": 24392, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746262521577, "end": 1746262521578, "duration": 1, "pid": 24392, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746262521578, "end": 1746262521579, "duration": 1, "pid": 24392, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746262521579, "end": 1746262521580, "duration": 1, "pid": 24392, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746262521581, "end": 1746262521581, "duration": 0, "pid": 24392, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746262521582, "end": 1746262521582, "duration": 0, "pid": 24392, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746262521583, "end": 1746262521583, "duration": 0, "pid": 24392, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746262521585, "end": 1746262521585, "duration": 0, "pid": 24392, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746262521586, "end": 1746262521586, "duration": 0, "pid": 24392, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746262521588, "end": 1746262521588, "duration": 0, "pid": 24392, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746262521590, "end": 1746262521591, "duration": 1, "pid": 24392, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746262521594, "end": 1746262521594, "duration": 0, "pid": 24392, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746262521594, "end": 1746262521594, "duration": 0, "pid": 24392, "index": 30}, {"name": "Load extend/application.js", "start": 1746262521595, "end": 1746262521701, "duration": 106, "pid": 24392, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746262521596, "end": 1746262521597, "duration": 1, "pid": 24392, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746262521598, "end": 1746262521600, "duration": 2, "pid": 24392, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746262521600, "end": 1746262521606, "duration": 6, "pid": 24392, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746262521608, "end": 1746262521615, "duration": 7, "pid": 24392, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746262521616, "end": 1746262521618, "duration": 2, "pid": 24392, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746262521619, "end": 1746262521621, "duration": 2, "pid": 24392, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746262521622, "end": 1746262521692, "duration": 70, "pid": 24392, "index": 38}, {"name": "Load extend/request.js", "start": 1746262521701, "end": 1746262521718, "duration": 17, "pid": 24392, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746262521708, "end": 1746262521710, "duration": 2, "pid": 24392, "index": 40}, {"name": "Load extend/response.js", "start": 1746262521718, "end": 1746262521736, "duration": 18, "pid": 24392, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746262521725, "end": 1746262521728, "duration": 3, "pid": 24392, "index": 42}, {"name": "Load extend/context.js", "start": 1746262521736, "end": 1746262521800, "duration": 64, "pid": 24392, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746262521737, "end": 1746262521754, "duration": 17, "pid": 24392, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746262521755, "end": 1746262521757, "duration": 2, "pid": 24392, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746262521758, "end": 1746262521758, "duration": 0, "pid": 24392, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746262521760, "end": 1746262521785, "duration": 25, "pid": 24392, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746262521786, "end": 1746262521787, "duration": 1, "pid": 24392, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746262521789, "end": 1746262521790, "duration": 1, "pid": 24392, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746262521791, "end": 1746262521794, "duration": 3, "pid": 24392, "index": 50}, {"name": "Load extend/helper.js", "start": 1746262521800, "end": 1746262521840, "duration": 40, "pid": 24392, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746262521801, "end": 1746262521824, "duration": 23, "pid": 24392, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746262521831, "end": 1746262521831, "duration": 0, "pid": 24392, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746262521832, "end": 1746262521833, "duration": 1, "pid": 24392, "index": 54}, {"name": "Load app.js", "start": 1746262521840, "end": 1746262521933, "duration": 93, "pid": 24392, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746262521841, "end": 1746262521841, "duration": 0, "pid": 24392, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746262521842, "end": 1746262521845, "duration": 3, "pid": 24392, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746262521846, "end": 1746262521859, "duration": 13, "pid": 24392, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746262521860, "end": 1746262521877, "duration": 17, "pid": 24392, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746262521877, "end": 1746262521893, "duration": 16, "pid": 24392, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746262521893, "end": 1746262521894, "duration": 1, "pid": 24392, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746262521895, "end": 1746262521897, "duration": 2, "pid": 24392, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746262521898, "end": 1746262521898, "duration": 0, "pid": 24392, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746262521899, "end": 1746262521899, "duration": 0, "pid": 24392, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746262521900, "end": 1746262521900, "duration": 0, "pid": 24392, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746262521901, "end": 1746262521902, "duration": 1, "pid": 24392, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746262521902, "end": 1746262521903, "duration": 1, "pid": 24392, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746262521903, "end": 1746262521904, "duration": 1, "pid": 24392, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746262521904, "end": 1746262521910, "duration": 6, "pid": 24392, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746262521910, "end": 1746262521926, "duration": 16, "pid": 24392, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746262521926, "end": 1746262521931, "duration": 5, "pid": 24392, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746262521944, "end": 1746262523257, "duration": 1313, "pid": 24392, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746262522517, "end": 1746262522634, "duration": 117, "pid": 24392, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746262522540, "end": 1746262523207, "duration": 667, "pid": 24392, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746262522662, "end": 1746262523273, "duration": 611, "pid": 24392, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746262522752, "end": 1746262523258, "duration": 506, "pid": 24392, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746262522850, "end": 1746262523227, "duration": 377, "pid": 24392, "index": 77}, {"name": "Load Service", "start": 1746262522850, "end": 1746262522959, "duration": 109, "pid": 24392, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746262522851, "end": 1746262522959, "duration": 108, "pid": 24392, "index": 79}, {"name": "Load Middleware", "start": 1746262522959, "end": 1746262523101, "duration": 142, "pid": 24392, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746262522959, "end": 1746262523087, "duration": 128, "pid": 24392, "index": 81}, {"name": "Load Controller", "start": 1746262523101, "end": 1746262523155, "duration": 54, "pid": 24392, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746262523101, "end": 1746262523155, "duration": 54, "pid": 24392, "index": 83}, {"name": "Load Router", "start": 1746262523155, "end": 1746262523160, "duration": 5, "pid": 24392, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746262523156, "end": 1746262523156, "duration": 0, "pid": 24392, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746262523157, "end": 1746262523207, "duration": 50, "pid": 24392, "index": 86}]