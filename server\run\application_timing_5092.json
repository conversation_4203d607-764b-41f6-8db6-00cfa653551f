[{"name": "Process Start", "start": 1746258466193, "end": 1746258468176, "duration": 1983, "pid": 5092, "index": 0}, {"name": "Application Start", "start": 1746258468178, "end": 1746258470430, "duration": 2252, "pid": 5092, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746258468197, "end": 1746258468239, "duration": 42, "pid": 5092, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746258468239, "end": 1746258468291, "duration": 52, "pid": 5092, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746258468240, "end": 1746258468241, "duration": 1, "pid": 5092, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746258468243, "end": 1746258468244, "duration": 1, "pid": 5092, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746258468245, "end": 1746258468246, "duration": 1, "pid": 5092, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746258468246, "end": 1746258468247, "duration": 1, "pid": 5092, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746258468252, "end": 1746258468254, "duration": 2, "pid": 5092, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746258468255, "end": 1746258468256, "duration": 1, "pid": 5092, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746258468256, "end": 1746258468257, "duration": 1, "pid": 5092, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746258468258, "end": 1746258468258, "duration": 0, "pid": 5092, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746258468259, "end": 1746258468260, "duration": 1, "pid": 5092, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746258468261, "end": 1746258468261, "duration": 0, "pid": 5092, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746258468263, "end": 1746258468263, "duration": 0, "pid": 5092, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746258468264, "end": 1746258468264, "duration": 0, "pid": 5092, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746258468265, "end": 1746258468266, "duration": 1, "pid": 5092, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746258468267, "end": 1746258468268, "duration": 1, "pid": 5092, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746258468270, "end": 1746258468270, "duration": 0, "pid": 5092, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746258468271, "end": 1746258468272, "duration": 1, "pid": 5092, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746258468273, "end": 1746258468273, "duration": 0, "pid": 5092, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746258468274, "end": 1746258468274, "duration": 0, "pid": 5092, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746258468275, "end": 1746258468275, "duration": 0, "pid": 5092, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746258468276, "end": 1746258468276, "duration": 0, "pid": 5092, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746258468277, "end": 1746258468278, "duration": 1, "pid": 5092, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746258468279, "end": 1746258468279, "duration": 0, "pid": 5092, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746258468280, "end": 1746258468281, "duration": 1, "pid": 5092, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746258468283, "end": 1746258468283, "duration": 0, "pid": 5092, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746258468287, "end": 1746258468287, "duration": 0, "pid": 5092, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746258468290, "end": 1746258468291, "duration": 1, "pid": 5092, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746258468291, "end": 1746258468291, "duration": 0, "pid": 5092, "index": 30}, {"name": "Load extend/application.js", "start": 1746258468292, "end": 1746258468396, "duration": 104, "pid": 5092, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746258468293, "end": 1746258468294, "duration": 1, "pid": 5092, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746258468295, "end": 1746258468296, "duration": 1, "pid": 5092, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746258468297, "end": 1746258468305, "duration": 8, "pid": 5092, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746258468306, "end": 1746258468313, "duration": 7, "pid": 5092, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746258468315, "end": 1746258468318, "duration": 3, "pid": 5092, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746258468319, "end": 1746258468321, "duration": 2, "pid": 5092, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746258468322, "end": 1746258468388, "duration": 66, "pid": 5092, "index": 38}, {"name": "Load extend/request.js", "start": 1746258468396, "end": 1746258468413, "duration": 17, "pid": 5092, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746258468404, "end": 1746258468406, "duration": 2, "pid": 5092, "index": 40}, {"name": "Load extend/response.js", "start": 1746258468413, "end": 1746258468431, "duration": 18, "pid": 5092, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746258468421, "end": 1746258468424, "duration": 3, "pid": 5092, "index": 42}, {"name": "Load extend/context.js", "start": 1746258468431, "end": 1746258468514, "duration": 83, "pid": 5092, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746258468432, "end": 1746258468453, "duration": 21, "pid": 5092, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746258468453, "end": 1746258468456, "duration": 3, "pid": 5092, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746258468457, "end": 1746258468457, "duration": 0, "pid": 5092, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746258468459, "end": 1746258468492, "duration": 33, "pid": 5092, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746258468494, "end": 1746258468496, "duration": 2, "pid": 5092, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746258468497, "end": 1746258468498, "duration": 1, "pid": 5092, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746258468500, "end": 1746258468506, "duration": 6, "pid": 5092, "index": 50}, {"name": "Load extend/helper.js", "start": 1746258468514, "end": 1746258468563, "duration": 49, "pid": 5092, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746258468515, "end": 1746258468547, "duration": 32, "pid": 5092, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746258468553, "end": 1746258468554, "duration": 1, "pid": 5092, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746258468555, "end": 1746258468555, "duration": 0, "pid": 5092, "index": 54}, {"name": "Load app.js", "start": 1746258468563, "end": 1746258468668, "duration": 105, "pid": 5092, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746258468564, "end": 1746258468564, "duration": 0, "pid": 5092, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746258468565, "end": 1746258468568, "duration": 3, "pid": 5092, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746258468569, "end": 1746258468584, "duration": 15, "pid": 5092, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746258468585, "end": 1746258468602, "duration": 17, "pid": 5092, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746258468603, "end": 1746258468618, "duration": 15, "pid": 5092, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746258468619, "end": 1746258468620, "duration": 1, "pid": 5092, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746258468620, "end": 1746258468623, "duration": 3, "pid": 5092, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746258468623, "end": 1746258468624, "duration": 1, "pid": 5092, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746258468624, "end": 1746258468625, "duration": 1, "pid": 5092, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746258468625, "end": 1746258468625, "duration": 0, "pid": 5092, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746258468627, "end": 1746258468627, "duration": 0, "pid": 5092, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746258468628, "end": 1746258468628, "duration": 0, "pid": 5092, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746258468629, "end": 1746258468629, "duration": 0, "pid": 5092, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746258468630, "end": 1746258468636, "duration": 6, "pid": 5092, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746258468637, "end": 1746258468659, "duration": 22, "pid": 5092, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746258468660, "end": 1746258468665, "duration": 5, "pid": 5092, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746258468683, "end": 1746258470413, "duration": 1730, "pid": 5092, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746258469529, "end": 1746258469641, "duration": 112, "pid": 5092, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746258469573, "end": 1746258470355, "duration": 782, "pid": 5092, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746258469676, "end": 1746258470430, "duration": 754, "pid": 5092, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746258469783, "end": 1746258470414, "duration": 631, "pid": 5092, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746258469909, "end": 1746258470377, "duration": 468, "pid": 5092, "index": 77}, {"name": "Load Service", "start": 1746258469910, "end": 1746258470044, "duration": 134, "pid": 5092, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746258469910, "end": 1746258470044, "duration": 134, "pid": 5092, "index": 79}, {"name": "Load Middleware", "start": 1746258470044, "end": 1746258470231, "duration": 187, "pid": 5092, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746258470044, "end": 1746258470214, "duration": 170, "pid": 5092, "index": 81}, {"name": "Load Controller", "start": 1746258470231, "end": 1746258470276, "duration": 45, "pid": 5092, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746258470231, "end": 1746258470276, "duration": 45, "pid": 5092, "index": 83}, {"name": "Load Router", "start": 1746258470276, "end": 1746258470282, "duration": 6, "pid": 5092, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746258470276, "end": 1746258470277, "duration": 1, "pid": 5092, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746258470278, "end": 1746258470355, "duration": 77, "pid": 5092, "index": 86}]