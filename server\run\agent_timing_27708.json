[{"name": "Process Start", "start": 1748229797889, "end": 1748229802659, "duration": 4770, "pid": 27708, "index": 0}, {"name": "Application Start", "start": 1748229802662, "end": 1748229804385, "duration": 1723, "pid": 27708, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748229802693, "end": 1748229802749, "duration": 56, "pid": 27708, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748229802749, "end": 1748229802797, "duration": 48, "pid": 27708, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748229802750, "end": 1748229802751, "duration": 1, "pid": 27708, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748229802753, "end": 1748229802754, "duration": 1, "pid": 27708, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748229802755, "end": 1748229802755, "duration": 0, "pid": 27708, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748229802756, "end": 1748229802756, "duration": 0, "pid": 27708, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748229802757, "end": 1748229802758, "duration": 1, "pid": 27708, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748229802759, "end": 1748229802759, "duration": 0, "pid": 27708, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748229802760, "end": 1748229802761, "duration": 1, "pid": 27708, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748229802763, "end": 1748229802764, "duration": 1, "pid": 27708, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748229802764, "end": 1748229802765, "duration": 1, "pid": 27708, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748229802766, "end": 1748229802766, "duration": 0, "pid": 27708, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748229802767, "end": 1748229802767, "duration": 0, "pid": 27708, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748229802768, "end": 1748229802768, "duration": 0, "pid": 27708, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748229802769, "end": 1748229802769, "duration": 0, "pid": 27708, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748229802770, "end": 1748229802771, "duration": 1, "pid": 27708, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748229802771, "end": 1748229802772, "duration": 1, "pid": 27708, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748229802772, "end": 1748229802773, "duration": 1, "pid": 27708, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748229802773, "end": 1748229802774, "duration": 1, "pid": 27708, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748229802774, "end": 1748229802775, "duration": 1, "pid": 27708, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748229802775, "end": 1748229802776, "duration": 1, "pid": 27708, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748229802778, "end": 1748229802780, "duration": 2, "pid": 27708, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748229802780, "end": 1748229802781, "duration": 1, "pid": 27708, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748229802783, "end": 1748229802783, "duration": 0, "pid": 27708, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748229802785, "end": 1748229802785, "duration": 0, "pid": 27708, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748229802787, "end": 1748229802788, "duration": 1, "pid": 27708, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748229802790, "end": 1748229802791, "duration": 1, "pid": 27708, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748229802796, "end": 1748229802797, "duration": 1, "pid": 27708, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748229802797, "end": 1748229802797, "duration": 0, "pid": 27708, "index": 30}, {"name": "Load extend/agent.js", "start": 1748229802799, "end": 1748229802914, "duration": 115, "pid": 27708, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/agent.js", "start": 1748229802801, "end": 1748229802803, "duration": 2, "pid": 27708, "index": 32}, {"name": "Require(28) node_modules/egg-schedule/app/extend/agent.js", "start": 1748229802805, "end": 1748229802900, "duration": 95, "pid": 27708, "index": 33}, {"name": "Require(29) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748229802901, "end": 1748229802903, "duration": 2, "pid": 27708, "index": 34}, {"name": "Load extend/context.js", "start": 1748229802915, "end": 1748229802998, "duration": 83, "pid": 27708, "index": 35}, {"name": "Require(30) node_modules/egg-security/app/extend/context.js", "start": 1748229802916, "end": 1748229802938, "duration": 22, "pid": 27708, "index": 36}, {"name": "Require(31) node_modules/egg-jsonp/app/extend/context.js", "start": 1748229802939, "end": 1748229802943, "duration": 4, "pid": 27708, "index": 37}, {"name": "Require(32) node_modules/egg-i18n/app/extend/context.js", "start": 1748229802944, "end": 1748229802945, "duration": 1, "pid": 27708, "index": 38}, {"name": "Require(33) node_modules/egg-multipart/app/extend/context.js", "start": 1748229802946, "end": 1748229802976, "duration": 30, "pid": 27708, "index": 39}, {"name": "Require(34) node_modules/egg-view/app/extend/context.js", "start": 1748229802978, "end": 1748229802981, "duration": 3, "pid": 27708, "index": 40}, {"name": "Require(35) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748229802984, "end": 1748229802985, "duration": 1, "pid": 27708, "index": 41}, {"name": "Require(36) node_modules/egg/app/extend/context.js", "start": 1748229802986, "end": 1748229802990, "duration": 4, "pid": 27708, "index": 42}, {"name": "Load agent.js", "start": 1748229802998, "end": 1748229803074, "duration": 76, "pid": 27708, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1748229802999, "end": 1748229803000, "duration": 1, "pid": 27708, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1748229803001, "end": 1748229803002, "duration": 1, "pid": 27708, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1748229803003, "end": 1748229803017, "duration": 14, "pid": 27708, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1748229803018, "end": 1748229803020, "duration": 2, "pid": 27708, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1748229803021, "end": 1748229803040, "duration": 19, "pid": 27708, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1748229803040, "end": 1748229803040, "duration": 0, "pid": 27708, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1748229803042, "end": 1748229803042, "duration": 0, "pid": 27708, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1748229803045, "end": 1748229803065, "duration": 20, "pid": 27708, "index": 51}, {"name": "Require(45) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748229803066, "end": 1748229803072, "duration": 6, "pid": 27708, "index": 52}, {"name": "Require(46) node_modules/egg/agent.js", "start": 1748229803073, "end": 1748229803073, "duration": 0, "pid": 27708, "index": 53}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748229803083, "end": 1748229804154, "duration": 1071, "pid": 27708, "index": 54}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748229803083, "end": 1748229804096, "duration": 1013, "pid": 27708, "index": 55}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1748229803083, "end": 1748229804364, "duration": 1281, "pid": 27708, "index": 56}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748229803821, "end": 1748229803917, "duration": 96, "pid": 27708, "index": 57}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748229803847, "end": 1748229804150, "duration": 303, "pid": 27708, "index": 58}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748229803958, "end": 1748229804380, "duration": 422, "pid": 27708, "index": 59}]