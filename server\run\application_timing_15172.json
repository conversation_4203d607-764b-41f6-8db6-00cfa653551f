[{"name": "Process Start", "start": 1746262523575, "end": 1746262525186, "duration": 1611, "pid": 15172, "index": 0}, {"name": "Application Start", "start": 1746262525187, "end": 1746262527082, "duration": 1895, "pid": 15172, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746262525214, "end": 1746262525265, "duration": 51, "pid": 15172, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746262525265, "end": 1746262525318, "duration": 53, "pid": 15172, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746262525266, "end": 1746262525267, "duration": 1, "pid": 15172, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746262525269, "end": 1746262525270, "duration": 1, "pid": 15172, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746262525271, "end": 1746262525272, "duration": 1, "pid": 15172, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746262525273, "end": 1746262525273, "duration": 0, "pid": 15172, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746262525275, "end": 1746262525276, "duration": 1, "pid": 15172, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746262525277, "end": 1746262525277, "duration": 0, "pid": 15172, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746262525278, "end": 1746262525279, "duration": 1, "pid": 15172, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746262525280, "end": 1746262525281, "duration": 1, "pid": 15172, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746262525282, "end": 1746262525282, "duration": 0, "pid": 15172, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746262525288, "end": 1746262525289, "duration": 1, "pid": 15172, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746262525290, "end": 1746262525291, "duration": 1, "pid": 15172, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746262525292, "end": 1746262525293, "duration": 1, "pid": 15172, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746262525294, "end": 1746262525295, "duration": 1, "pid": 15172, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746262525296, "end": 1746262525296, "duration": 0, "pid": 15172, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746262525298, "end": 1746262525298, "duration": 0, "pid": 15172, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746262525299, "end": 1746262525300, "duration": 1, "pid": 15172, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746262525300, "end": 1746262525301, "duration": 1, "pid": 15172, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746262525301, "end": 1746262525302, "duration": 1, "pid": 15172, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746262525302, "end": 1746262525303, "duration": 1, "pid": 15172, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746262525304, "end": 1746262525304, "duration": 0, "pid": 15172, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746262525305, "end": 1746262525305, "duration": 0, "pid": 15172, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746262525307, "end": 1746262525307, "duration": 0, "pid": 15172, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746262525308, "end": 1746262525308, "duration": 0, "pid": 15172, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746262525310, "end": 1746262525310, "duration": 0, "pid": 15172, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746262525314, "end": 1746262525314, "duration": 0, "pid": 15172, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746262525318, "end": 1746262525318, "duration": 0, "pid": 15172, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746262525318, "end": 1746262525318, "duration": 0, "pid": 15172, "index": 30}, {"name": "Load extend/application.js", "start": 1746262525319, "end": 1746262525439, "duration": 120, "pid": 15172, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746262525320, "end": 1746262525321, "duration": 1, "pid": 15172, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746262525322, "end": 1746262525324, "duration": 2, "pid": 15172, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746262525326, "end": 1746262525335, "duration": 9, "pid": 15172, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746262525337, "end": 1746262525347, "duration": 10, "pid": 15172, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746262525349, "end": 1746262525352, "duration": 3, "pid": 15172, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746262525353, "end": 1746262525356, "duration": 3, "pid": 15172, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746262525358, "end": 1746262525429, "duration": 71, "pid": 15172, "index": 38}, {"name": "Load extend/request.js", "start": 1746262525439, "end": 1746262525456, "duration": 17, "pid": 15172, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746262525447, "end": 1746262525449, "duration": 2, "pid": 15172, "index": 40}, {"name": "Load extend/response.js", "start": 1746262525456, "end": 1746262525476, "duration": 20, "pid": 15172, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746262525464, "end": 1746262525468, "duration": 4, "pid": 15172, "index": 42}, {"name": "Load extend/context.js", "start": 1746262525476, "end": 1746262525549, "duration": 73, "pid": 15172, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746262525477, "end": 1746262525496, "duration": 19, "pid": 15172, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746262525496, "end": 1746262525499, "duration": 3, "pid": 15172, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746262525500, "end": 1746262525501, "duration": 1, "pid": 15172, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746262525503, "end": 1746262525531, "duration": 28, "pid": 15172, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746262525533, "end": 1746262525535, "duration": 2, "pid": 15172, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746262525537, "end": 1746262525537, "duration": 0, "pid": 15172, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746262525538, "end": 1746262525541, "duration": 3, "pid": 15172, "index": 50}, {"name": "Load extend/helper.js", "start": 1746262525549, "end": 1746262525588, "duration": 39, "pid": 15172, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746262525550, "end": 1746262525574, "duration": 24, "pid": 15172, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746262525579, "end": 1746262525580, "duration": 1, "pid": 15172, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746262525580, "end": 1746262525581, "duration": 1, "pid": 15172, "index": 54}, {"name": "Load app.js", "start": 1746262525588, "end": 1746262525681, "duration": 93, "pid": 15172, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746262525589, "end": 1746262525589, "duration": 0, "pid": 15172, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746262525590, "end": 1746262525592, "duration": 2, "pid": 15172, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746262525593, "end": 1746262525609, "duration": 16, "pid": 15172, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746262525609, "end": 1746262525624, "duration": 15, "pid": 15172, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746262525624, "end": 1746262525638, "duration": 14, "pid": 15172, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746262525639, "end": 1746262525639, "duration": 0, "pid": 15172, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746262525640, "end": 1746262525642, "duration": 2, "pid": 15172, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746262525643, "end": 1746262525643, "duration": 0, "pid": 15172, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746262525644, "end": 1746262525644, "duration": 0, "pid": 15172, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746262525644, "end": 1746262525645, "duration": 1, "pid": 15172, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746262525646, "end": 1746262525646, "duration": 0, "pid": 15172, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746262525647, "end": 1746262525647, "duration": 0, "pid": 15172, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746262525648, "end": 1746262525649, "duration": 1, "pid": 15172, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746262525649, "end": 1746262525655, "duration": 6, "pid": 15172, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746262525656, "end": 1746262525673, "duration": 17, "pid": 15172, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746262525673, "end": 1746262525679, "duration": 6, "pid": 15172, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746262525695, "end": 1746262527066, "duration": 1371, "pid": 15172, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746262526294, "end": 1746262526370, "duration": 76, "pid": 15172, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746262526319, "end": 1746262527022, "duration": 703, "pid": 15172, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746262526395, "end": 1746262527081, "duration": 686, "pid": 15172, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746262526479, "end": 1746262527068, "duration": 589, "pid": 15172, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746262526579, "end": 1746262527039, "duration": 460, "pid": 15172, "index": 77}, {"name": "Load Service", "start": 1746262526579, "end": 1746262526728, "duration": 149, "pid": 15172, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746262526579, "end": 1746262526728, "duration": 149, "pid": 15172, "index": 79}, {"name": "Load Middleware", "start": 1746262526729, "end": 1746262526917, "duration": 188, "pid": 15172, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746262526729, "end": 1746262526902, "duration": 173, "pid": 15172, "index": 81}, {"name": "Load Controller", "start": 1746262526917, "end": 1746262526963, "duration": 46, "pid": 15172, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746262526917, "end": 1746262526963, "duration": 46, "pid": 15172, "index": 83}, {"name": "Load Router", "start": 1746262526963, "end": 1746262526970, "duration": 7, "pid": 15172, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746262526964, "end": 1746262526964, "duration": 0, "pid": 15172, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746262526965, "end": 1746262527022, "duration": 57, "pid": 15172, "index": 86}]