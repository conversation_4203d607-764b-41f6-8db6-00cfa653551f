[{"name": "Process Start", "start": 1746257629142, "end": 1746257633559, "duration": 4417, "pid": 14112, "index": 0}, {"name": "Application Start", "start": 1746257633562, "end": 1746257637660, "duration": 4098, "pid": 14112, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746257633598, "end": 1746257633663, "duration": 65, "pid": 14112, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746257633663, "end": 1746257633743, "duration": 80, "pid": 14112, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746257633665, "end": 1746257633666, "duration": 1, "pid": 14112, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746257633673, "end": 1746257633674, "duration": 1, "pid": 14112, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746257633677, "end": 1746257633677, "duration": 0, "pid": 14112, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746257633679, "end": 1746257633679, "duration": 0, "pid": 14112, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746257633681, "end": 1746257633682, "duration": 1, "pid": 14112, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746257633684, "end": 1746257633685, "duration": 1, "pid": 14112, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746257633686, "end": 1746257633686, "duration": 0, "pid": 14112, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746257633688, "end": 1746257633688, "duration": 0, "pid": 14112, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746257633690, "end": 1746257633691, "duration": 1, "pid": 14112, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746257633693, "end": 1746257633694, "duration": 1, "pid": 14112, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746257633695, "end": 1746257633696, "duration": 1, "pid": 14112, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746257633697, "end": 1746257633698, "duration": 1, "pid": 14112, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746257633700, "end": 1746257633701, "duration": 1, "pid": 14112, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746257633702, "end": 1746257633703, "duration": 1, "pid": 14112, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746257633705, "end": 1746257633705, "duration": 0, "pid": 14112, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746257633709, "end": 1746257633710, "duration": 1, "pid": 14112, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746257633711, "end": 1746257633712, "duration": 1, "pid": 14112, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746257633713, "end": 1746257633714, "duration": 1, "pid": 14112, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746257633718, "end": 1746257633718, "duration": 0, "pid": 14112, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746257633719, "end": 1746257633720, "duration": 1, "pid": 14112, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746257633721, "end": 1746257633722, "duration": 1, "pid": 14112, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746257633726, "end": 1746257633726, "duration": 0, "pid": 14112, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746257633728, "end": 1746257633728, "duration": 0, "pid": 14112, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746257633731, "end": 1746257633732, "duration": 1, "pid": 14112, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746257633736, "end": 1746257633737, "duration": 1, "pid": 14112, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746257633742, "end": 1746257633742, "duration": 0, "pid": 14112, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746257633743, "end": 1746257633743, "duration": 0, "pid": 14112, "index": 30}, {"name": "Load extend/application.js", "start": 1746257633746, "end": 1746257633929, "duration": 183, "pid": 14112, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746257633747, "end": 1746257633748, "duration": 1, "pid": 14112, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746257633749, "end": 1746257633753, "duration": 4, "pid": 14112, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746257633754, "end": 1746257633766, "duration": 12, "pid": 14112, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746257633769, "end": 1746257633781, "duration": 12, "pid": 14112, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746257633784, "end": 1746257633787, "duration": 3, "pid": 14112, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746257633789, "end": 1746257633794, "duration": 5, "pid": 14112, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746257633795, "end": 1746257633913, "duration": 118, "pid": 14112, "index": 38}, {"name": "Load extend/request.js", "start": 1746257633929, "end": 1746257633960, "duration": 31, "pid": 14112, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746257633943, "end": 1746257633946, "duration": 3, "pid": 14112, "index": 40}, {"name": "Load extend/response.js", "start": 1746257633960, "end": 1746257634003, "duration": 43, "pid": 14112, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746257633982, "end": 1746257633990, "duration": 8, "pid": 14112, "index": 42}, {"name": "Load extend/context.js", "start": 1746257634003, "end": 1746257634173, "duration": 170, "pid": 14112, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746257634006, "end": 1746257634045, "duration": 39, "pid": 14112, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746257634046, "end": 1746257634052, "duration": 6, "pid": 14112, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746257634054, "end": 1746257634056, "duration": 2, "pid": 14112, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746257634064, "end": 1746257634136, "duration": 72, "pid": 14112, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746257634138, "end": 1746257634143, "duration": 5, "pid": 14112, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746257634146, "end": 1746257634147, "duration": 1, "pid": 14112, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746257634153, "end": 1746257634160, "duration": 7, "pid": 14112, "index": 50}, {"name": "Load extend/helper.js", "start": 1746257634175, "end": 1746257634278, "duration": 103, "pid": 14112, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746257634177, "end": 1746257634220, "duration": 43, "pid": 14112, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746257634233, "end": 1746257634234, "duration": 1, "pid": 14112, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746257634235, "end": 1746257634236, "duration": 1, "pid": 14112, "index": 54}, {"name": "Load app.js", "start": 1746257634279, "end": 1746257634462, "duration": 183, "pid": 14112, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746257634279, "end": 1746257634280, "duration": 1, "pid": 14112, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746257634281, "end": 1746257634286, "duration": 5, "pid": 14112, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746257634297, "end": 1746257634327, "duration": 30, "pid": 14112, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746257634328, "end": 1746257634352, "duration": 24, "pid": 14112, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746257634353, "end": 1746257634377, "duration": 24, "pid": 14112, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746257634377, "end": 1746257634379, "duration": 2, "pid": 14112, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746257634380, "end": 1746257634384, "duration": 4, "pid": 14112, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746257634385, "end": 1746257634385, "duration": 0, "pid": 14112, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746257634386, "end": 1746257634389, "duration": 3, "pid": 14112, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746257634390, "end": 1746257634391, "duration": 1, "pid": 14112, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746257634393, "end": 1746257634394, "duration": 1, "pid": 14112, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746257634396, "end": 1746257634396, "duration": 0, "pid": 14112, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746257634397, "end": 1746257634398, "duration": 1, "pid": 14112, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746257634399, "end": 1746257634415, "duration": 16, "pid": 14112, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746257634417, "end": 1746257634449, "duration": 32, "pid": 14112, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746257634450, "end": 1746257634459, "duration": 9, "pid": 14112, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746257634485, "end": 1746257637628, "duration": 3143, "pid": 14112, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746257635831, "end": 1746257636189, "duration": 358, "pid": 14112, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746257635933, "end": 1746257637516, "duration": 1583, "pid": 14112, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746257636247, "end": 1746257637660, "duration": 1413, "pid": 14112, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746257636403, "end": 1746257637630, "duration": 1227, "pid": 14112, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746257636644, "end": 1746257637570, "duration": 926, "pid": 14112, "index": 77}, {"name": "Load Service", "start": 1746257636644, "end": 1746257636972, "duration": 328, "pid": 14112, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746257636645, "end": 1746257636972, "duration": 327, "pid": 14112, "index": 79}, {"name": "Load Middleware", "start": 1746257636972, "end": 1746257637262, "duration": 290, "pid": 14112, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746257636973, "end": 1746257637235, "duration": 262, "pid": 14112, "index": 81}, {"name": "Load Controller", "start": 1746257637262, "end": 1746257637333, "duration": 71, "pid": 14112, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746257637262, "end": 1746257637333, "duration": 71, "pid": 14112, "index": 83}, {"name": "Load Router", "start": 1746257637333, "end": 1746257637347, "duration": 14, "pid": 14112, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746257637334, "end": 1746257637336, "duration": 2, "pid": 14112, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746257637337, "end": 1746257637516, "duration": 179, "pid": 14112, "index": 86}]