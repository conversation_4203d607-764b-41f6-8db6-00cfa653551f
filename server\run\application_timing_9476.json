[{"name": "Process Start", "start": 1748231887635, "end": 1748231889667, "duration": 2032, "pid": 9476, "index": 0}, {"name": "Application Start", "start": 1748231889669, "end": 1748231892101, "duration": 2432, "pid": 9476, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748231889688, "end": 1748231889730, "duration": 42, "pid": 9476, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748231889730, "end": 1748231889776, "duration": 46, "pid": 9476, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748231889731, "end": 1748231889731, "duration": 0, "pid": 9476, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748231889733, "end": 1748231889734, "duration": 1, "pid": 9476, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748231889735, "end": 1748231889736, "duration": 1, "pid": 9476, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748231889737, "end": 1748231889737, "duration": 0, "pid": 9476, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748231889738, "end": 1748231889739, "duration": 1, "pid": 9476, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748231889740, "end": 1748231889740, "duration": 0, "pid": 9476, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748231889741, "end": 1748231889741, "duration": 0, "pid": 9476, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748231889742, "end": 1748231889743, "duration": 1, "pid": 9476, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748231889744, "end": 1748231889745, "duration": 1, "pid": 9476, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748231889746, "end": 1748231889746, "duration": 0, "pid": 9476, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748231889747, "end": 1748231889747, "duration": 0, "pid": 9476, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748231889748, "end": 1748231889748, "duration": 0, "pid": 9476, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748231889749, "end": 1748231889750, "duration": 1, "pid": 9476, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748231889750, "end": 1748231889751, "duration": 1, "pid": 9476, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748231889751, "end": 1748231889752, "duration": 1, "pid": 9476, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748231889753, "end": 1748231889754, "duration": 1, "pid": 9476, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748231889754, "end": 1748231889755, "duration": 1, "pid": 9476, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748231889755, "end": 1748231889756, "duration": 1, "pid": 9476, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748231889756, "end": 1748231889757, "duration": 1, "pid": 9476, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748231889758, "end": 1748231889759, "duration": 1, "pid": 9476, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748231889760, "end": 1748231889762, "duration": 2, "pid": 9476, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748231889763, "end": 1748231889763, "duration": 0, "pid": 9476, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748231889765, "end": 1748231889765, "duration": 0, "pid": 9476, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748231889767, "end": 1748231889768, "duration": 1, "pid": 9476, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748231889770, "end": 1748231889771, "duration": 1, "pid": 9476, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748231889775, "end": 1748231889776, "duration": 1, "pid": 9476, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748231889776, "end": 1748231889776, "duration": 0, "pid": 9476, "index": 30}, {"name": "Load extend/application.js", "start": 1748231889779, "end": 1748231889915, "duration": 136, "pid": 9476, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748231889780, "end": 1748231889791, "duration": 11, "pid": 9476, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748231889792, "end": 1748231889795, "duration": 3, "pid": 9476, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748231889796, "end": 1748231889804, "duration": 8, "pid": 9476, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748231889805, "end": 1748231889815, "duration": 10, "pid": 9476, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748231889816, "end": 1748231889819, "duration": 3, "pid": 9476, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748231889820, "end": 1748231889822, "duration": 2, "pid": 9476, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748231889823, "end": 1748231889904, "duration": 81, "pid": 9476, "index": 38}, {"name": "Load extend/request.js", "start": 1748231889915, "end": 1748231889933, "duration": 18, "pid": 9476, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748231889923, "end": 1748231889925, "duration": 2, "pid": 9476, "index": 40}, {"name": "Load extend/response.js", "start": 1748231889933, "end": 1748231889952, "duration": 19, "pid": 9476, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748231889941, "end": 1748231889946, "duration": 5, "pid": 9476, "index": 42}, {"name": "Load extend/context.js", "start": 1748231889953, "end": 1748231890037, "duration": 84, "pid": 9476, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748231889954, "end": 1748231889976, "duration": 22, "pid": 9476, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748231889977, "end": 1748231889980, "duration": 3, "pid": 9476, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748231889981, "end": 1748231889982, "duration": 1, "pid": 9476, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748231889983, "end": 1748231890015, "duration": 32, "pid": 9476, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748231890016, "end": 1748231890018, "duration": 2, "pid": 9476, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748231890020, "end": 1748231890021, "duration": 1, "pid": 9476, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748231890022, "end": 1748231890026, "duration": 4, "pid": 9476, "index": 50}, {"name": "Load extend/helper.js", "start": 1748231890037, "end": 1748231890093, "duration": 56, "pid": 9476, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748231890038, "end": 1748231890074, "duration": 36, "pid": 9476, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748231890081, "end": 1748231890082, "duration": 1, "pid": 9476, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748231890082, "end": 1748231890083, "duration": 1, "pid": 9476, "index": 54}, {"name": "Load app.js", "start": 1748231890093, "end": 1748231890214, "duration": 121, "pid": 9476, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748231890094, "end": 1748231890095, "duration": 1, "pid": 9476, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748231890096, "end": 1748231890100, "duration": 4, "pid": 9476, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748231890101, "end": 1748231890126, "duration": 25, "pid": 9476, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748231890127, "end": 1748231890149, "duration": 22, "pid": 9476, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748231890150, "end": 1748231890168, "duration": 18, "pid": 9476, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748231890168, "end": 1748231890171, "duration": 3, "pid": 9476, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748231890172, "end": 1748231890176, "duration": 4, "pid": 9476, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748231890176, "end": 1748231890177, "duration": 1, "pid": 9476, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748231890178, "end": 1748231890178, "duration": 0, "pid": 9476, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748231890179, "end": 1748231890179, "duration": 0, "pid": 9476, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748231890181, "end": 1748231890181, "duration": 0, "pid": 9476, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748231890181, "end": 1748231890182, "duration": 1, "pid": 9476, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748231890182, "end": 1748231890183, "duration": 1, "pid": 9476, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748231890184, "end": 1748231890190, "duration": 6, "pid": 9476, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748231890191, "end": 1748231890207, "duration": 16, "pid": 9476, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748231890207, "end": 1748231890213, "duration": 6, "pid": 9476, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748231890230, "end": 1748231892079, "duration": 1849, "pid": 9476, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748231891019, "end": 1748231891105, "duration": 86, "pid": 9476, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748231891046, "end": 1748231891988, "duration": 942, "pid": 9476, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748231891140, "end": 1748231892100, "duration": 960, "pid": 9476, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748231891266, "end": 1748231892075, "duration": 809, "pid": 9476, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748231891397, "end": 1748231892028, "duration": 631, "pid": 9476, "index": 77}, {"name": "Load Service", "start": 1748231891397, "end": 1748231891544, "duration": 147, "pid": 9476, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748231891398, "end": 1748231891544, "duration": 146, "pid": 9476, "index": 79}, {"name": "Load Middleware", "start": 1748231891545, "end": 1748231891765, "duration": 220, "pid": 9476, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748231891545, "end": 1748231891744, "duration": 199, "pid": 9476, "index": 81}, {"name": "Load Controller", "start": 1748231891765, "end": 1748231891876, "duration": 111, "pid": 9476, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748231891765, "end": 1748231891876, "duration": 111, "pid": 9476, "index": 83}, {"name": "Load Router", "start": 1748231891876, "end": 1748231891887, "duration": 11, "pid": 9476, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748231891877, "end": 1748231891878, "duration": 1, "pid": 9476, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748231891880, "end": 1748231891988, "duration": 108, "pid": 9476, "index": 86}]