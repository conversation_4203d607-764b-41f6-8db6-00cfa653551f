[{"name": "Process Start", "start": 1748231880915, "end": 1748231885286, "duration": 4371, "pid": 28204, "index": 0}, {"name": "Application Start", "start": 1748231885288, "end": 1748231887276, "duration": 1988, "pid": 28204, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748231885314, "end": 1748231885352, "duration": 38, "pid": 28204, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748231885352, "end": 1748231885400, "duration": 48, "pid": 28204, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748231885353, "end": 1748231885353, "duration": 0, "pid": 28204, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748231885355, "end": 1748231885356, "duration": 1, "pid": 28204, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748231885357, "end": 1748231885358, "duration": 1, "pid": 28204, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748231885361, "end": 1748231885361, "duration": 0, "pid": 28204, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748231885363, "end": 1748231885363, "duration": 0, "pid": 28204, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748231885364, "end": 1748231885365, "duration": 1, "pid": 28204, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748231885365, "end": 1748231885366, "duration": 1, "pid": 28204, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748231885367, "end": 1748231885367, "duration": 0, "pid": 28204, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748231885368, "end": 1748231885369, "duration": 1, "pid": 28204, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748231885370, "end": 1748231885370, "duration": 0, "pid": 28204, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748231885372, "end": 1748231885373, "duration": 1, "pid": 28204, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748231885374, "end": 1748231885375, "duration": 1, "pid": 28204, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748231885376, "end": 1748231885377, "duration": 1, "pid": 28204, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748231885378, "end": 1748231885379, "duration": 1, "pid": 28204, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748231885379, "end": 1748231885380, "duration": 1, "pid": 28204, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748231885381, "end": 1748231885381, "duration": 0, "pid": 28204, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748231885382, "end": 1748231885382, "duration": 0, "pid": 28204, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748231885383, "end": 1748231885383, "duration": 0, "pid": 28204, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748231885384, "end": 1748231885384, "duration": 0, "pid": 28204, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748231885385, "end": 1748231885386, "duration": 1, "pid": 28204, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748231885386, "end": 1748231885387, "duration": 1, "pid": 28204, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748231885389, "end": 1748231885389, "duration": 0, "pid": 28204, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748231885390, "end": 1748231885391, "duration": 1, "pid": 28204, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748231885393, "end": 1748231885394, "duration": 1, "pid": 28204, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748231885396, "end": 1748231885396, "duration": 0, "pid": 28204, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748231885399, "end": 1748231885400, "duration": 1, "pid": 28204, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748231885400, "end": 1748231885400, "duration": 0, "pid": 28204, "index": 30}, {"name": "Load extend/agent.js", "start": 1748231885401, "end": 1748231885532, "duration": 131, "pid": 28204, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/agent.js", "start": 1748231885403, "end": 1748231885406, "duration": 3, "pid": 28204, "index": 32}, {"name": "Require(28) node_modules/egg-schedule/app/extend/agent.js", "start": 1748231885408, "end": 1748231885516, "duration": 108, "pid": 28204, "index": 33}, {"name": "Require(29) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748231885518, "end": 1748231885520, "duration": 2, "pid": 28204, "index": 34}, {"name": "Load extend/context.js", "start": 1748231885532, "end": 1748231885634, "duration": 102, "pid": 28204, "index": 35}, {"name": "Require(30) node_modules/egg-security/app/extend/context.js", "start": 1748231885533, "end": 1748231885559, "duration": 26, "pid": 28204, "index": 36}, {"name": "Require(31) node_modules/egg-jsonp/app/extend/context.js", "start": 1748231885560, "end": 1748231885566, "duration": 6, "pid": 28204, "index": 37}, {"name": "Require(32) node_modules/egg-i18n/app/extend/context.js", "start": 1748231885567, "end": 1748231885568, "duration": 1, "pid": 28204, "index": 38}, {"name": "Require(33) node_modules/egg-multipart/app/extend/context.js", "start": 1748231885570, "end": 1748231885612, "duration": 42, "pid": 28204, "index": 39}, {"name": "Require(34) node_modules/egg-view/app/extend/context.js", "start": 1748231885614, "end": 1748231885617, "duration": 3, "pid": 28204, "index": 40}, {"name": "Require(35) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748231885618, "end": 1748231885620, "duration": 2, "pid": 28204, "index": 41}, {"name": "Require(36) node_modules/egg/app/extend/context.js", "start": 1748231885622, "end": 1748231885627, "duration": 5, "pid": 28204, "index": 42}, {"name": "Load agent.js", "start": 1748231885634, "end": 1748231885721, "duration": 87, "pid": 28204, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1748231885635, "end": 1748231885636, "duration": 1, "pid": 28204, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1748231885638, "end": 1748231885638, "duration": 0, "pid": 28204, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1748231885639, "end": 1748231885658, "duration": 19, "pid": 28204, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1748231885659, "end": 1748231885662, "duration": 3, "pid": 28204, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1748231885663, "end": 1748231885683, "duration": 20, "pid": 28204, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1748231885683, "end": 1748231885684, "duration": 1, "pid": 28204, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1748231885685, "end": 1748231885685, "duration": 0, "pid": 28204, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1748231885687, "end": 1748231885712, "duration": 25, "pid": 28204, "index": 51}, {"name": "Require(45) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748231885713, "end": 1748231885719, "duration": 6, "pid": 28204, "index": 52}, {"name": "Require(46) node_modules/egg/agent.js", "start": 1748231885720, "end": 1748231885720, "duration": 0, "pid": 28204, "index": 53}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748231885729, "end": 1748231886988, "duration": 1259, "pid": 28204, "index": 54}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748231885730, "end": 1748231886919, "duration": 1189, "pid": 28204, "index": 55}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1748231885730, "end": 1748231887252, "duration": 1522, "pid": 28204, "index": 56}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748231886595, "end": 1748231886721, "duration": 126, "pid": 28204, "index": 57}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748231886623, "end": 1748231886984, "duration": 361, "pid": 28204, "index": 58}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748231886764, "end": 1748231887272, "duration": 508, "pid": 28204, "index": 59}]