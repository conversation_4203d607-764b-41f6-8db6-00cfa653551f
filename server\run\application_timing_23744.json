[{"name": "Process Start", "start": 1746261903951, "end": 1746261905962, "duration": 2011, "pid": 23744, "index": 0}, {"name": "Application Start", "start": 1746261905964, "end": 1746261908592, "duration": 2628, "pid": 23744, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746261905988, "end": 1746261906027, "duration": 39, "pid": 23744, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746261906027, "end": 1746261906084, "duration": 57, "pid": 23744, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746261906028, "end": 1746261906029, "duration": 1, "pid": 23744, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746261906031, "end": 1746261906031, "duration": 0, "pid": 23744, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746261906033, "end": 1746261906034, "duration": 1, "pid": 23744, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746261906035, "end": 1746261906035, "duration": 0, "pid": 23744, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746261906040, "end": 1746261906041, "duration": 1, "pid": 23744, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746261906041, "end": 1746261906042, "duration": 1, "pid": 23744, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746261906043, "end": 1746261906044, "duration": 1, "pid": 23744, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746261906045, "end": 1746261906046, "duration": 1, "pid": 23744, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746261906047, "end": 1746261906048, "duration": 1, "pid": 23744, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746261906050, "end": 1746261906050, "duration": 0, "pid": 23744, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746261906051, "end": 1746261906052, "duration": 1, "pid": 23744, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746261906053, "end": 1746261906054, "duration": 1, "pid": 23744, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746261906055, "end": 1746261906056, "duration": 1, "pid": 23744, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746261906057, "end": 1746261906057, "duration": 0, "pid": 23744, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746261906058, "end": 1746261906059, "duration": 1, "pid": 23744, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746261906060, "end": 1746261906061, "duration": 1, "pid": 23744, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746261906062, "end": 1746261906062, "duration": 0, "pid": 23744, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746261906063, "end": 1746261906064, "duration": 1, "pid": 23744, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746261906066, "end": 1746261906067, "duration": 1, "pid": 23744, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746261906068, "end": 1746261906069, "duration": 1, "pid": 23744, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746261906070, "end": 1746261906071, "duration": 1, "pid": 23744, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746261906072, "end": 1746261906072, "duration": 0, "pid": 23744, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746261906074, "end": 1746261906074, "duration": 0, "pid": 23744, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746261906076, "end": 1746261906076, "duration": 0, "pid": 23744, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746261906079, "end": 1746261906079, "duration": 0, "pid": 23744, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746261906084, "end": 1746261906084, "duration": 0, "pid": 23744, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746261906084, "end": 1746261906084, "duration": 0, "pid": 23744, "index": 30}, {"name": "Load extend/application.js", "start": 1746261906086, "end": 1746261906224, "duration": 138, "pid": 23744, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746261906088, "end": 1746261906088, "duration": 0, "pid": 23744, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746261906089, "end": 1746261906091, "duration": 2, "pid": 23744, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746261906092, "end": 1746261906100, "duration": 8, "pid": 23744, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746261906102, "end": 1746261906110, "duration": 8, "pid": 23744, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746261906112, "end": 1746261906114, "duration": 2, "pid": 23744, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746261906117, "end": 1746261906120, "duration": 3, "pid": 23744, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746261906122, "end": 1746261906213, "duration": 91, "pid": 23744, "index": 38}, {"name": "Load extend/request.js", "start": 1746261906224, "end": 1746261906244, "duration": 20, "pid": 23744, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746261906234, "end": 1746261906237, "duration": 3, "pid": 23744, "index": 40}, {"name": "Load extend/response.js", "start": 1746261906244, "end": 1746261906268, "duration": 24, "pid": 23744, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746261906254, "end": 1746261906259, "duration": 5, "pid": 23744, "index": 42}, {"name": "Load extend/context.js", "start": 1746261906268, "end": 1746261906357, "duration": 89, "pid": 23744, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746261906269, "end": 1746261906289, "duration": 20, "pid": 23744, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746261906290, "end": 1746261906292, "duration": 2, "pid": 23744, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746261906293, "end": 1746261906294, "duration": 1, "pid": 23744, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746261906296, "end": 1746261906333, "duration": 37, "pid": 23744, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746261906334, "end": 1746261906337, "duration": 3, "pid": 23744, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746261906340, "end": 1746261906340, "duration": 0, "pid": 23744, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746261906342, "end": 1746261906346, "duration": 4, "pid": 23744, "index": 50}, {"name": "Load extend/helper.js", "start": 1746261906357, "end": 1746261906412, "duration": 55, "pid": 23744, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746261906358, "end": 1746261906392, "duration": 34, "pid": 23744, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746261906400, "end": 1746261906401, "duration": 1, "pid": 23744, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746261906402, "end": 1746261906403, "duration": 1, "pid": 23744, "index": 54}, {"name": "Load app.js", "start": 1746261906412, "end": 1746261906548, "duration": 136, "pid": 23744, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746261906413, "end": 1746261906414, "duration": 1, "pid": 23744, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746261906414, "end": 1746261906420, "duration": 6, "pid": 23744, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746261906422, "end": 1746261906443, "duration": 21, "pid": 23744, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746261906444, "end": 1746261906468, "duration": 24, "pid": 23744, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746261906469, "end": 1746261906488, "duration": 19, "pid": 23744, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746261906488, "end": 1746261906490, "duration": 2, "pid": 23744, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746261906490, "end": 1746261906494, "duration": 4, "pid": 23744, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746261906495, "end": 1746261906495, "duration": 0, "pid": 23744, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746261906496, "end": 1746261906496, "duration": 0, "pid": 23744, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746261906497, "end": 1746261906498, "duration": 1, "pid": 23744, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746261906500, "end": 1746261906500, "duration": 0, "pid": 23744, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746261906501, "end": 1746261906501, "duration": 0, "pid": 23744, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746261906502, "end": 1746261906502, "duration": 0, "pid": 23744, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746261906503, "end": 1746261906511, "duration": 8, "pid": 23744, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746261906513, "end": 1746261906538, "duration": 25, "pid": 23744, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746261906539, "end": 1746261906546, "duration": 7, "pid": 23744, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746261906570, "end": 1746261908568, "duration": 1998, "pid": 23744, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746261907601, "end": 1746261907707, "duration": 106, "pid": 23744, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746261907635, "end": 1746261908506, "duration": 871, "pid": 23744, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746261907743, "end": 1746261908591, "duration": 848, "pid": 23744, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746261907841, "end": 1746261908570, "duration": 729, "pid": 23744, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746261907944, "end": 1746261908530, "duration": 586, "pid": 23744, "index": 77}, {"name": "Load Service", "start": 1746261907944, "end": 1746261908075, "duration": 131, "pid": 23744, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746261907944, "end": 1746261908075, "duration": 131, "pid": 23744, "index": 79}, {"name": "Load Middleware", "start": 1746261908075, "end": 1746261908346, "duration": 271, "pid": 23744, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746261908075, "end": 1746261908318, "duration": 243, "pid": 23744, "index": 81}, {"name": "Load Controller", "start": 1746261908346, "end": 1746261908412, "duration": 66, "pid": 23744, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746261908346, "end": 1746261908412, "duration": 66, "pid": 23744, "index": 83}, {"name": "Load Router", "start": 1746261908412, "end": 1746261908423, "duration": 11, "pid": 23744, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746261908413, "end": 1746261908416, "duration": 3, "pid": 23744, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746261908418, "end": 1746261908506, "duration": 88, "pid": 23744, "index": 86}]