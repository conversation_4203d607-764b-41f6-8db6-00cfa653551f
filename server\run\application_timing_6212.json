[{"name": "Process Start", "start": 1746257638002, "end": 1746257641106, "duration": 3104, "pid": 6212, "index": 0}, {"name": "Application Start", "start": 1746257641108, "end": 1746257647150, "duration": 6042, "pid": 6212, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746257641140, "end": 1746257641192, "duration": 52, "pid": 6212, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746257641192, "end": 1746257641263, "duration": 71, "pid": 6212, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746257641193, "end": 1746257641194, "duration": 1, "pid": 6212, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746257641196, "end": 1746257641196, "duration": 0, "pid": 6212, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746257641198, "end": 1746257641199, "duration": 1, "pid": 6212, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746257641200, "end": 1746257641201, "duration": 1, "pid": 6212, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746257641203, "end": 1746257641204, "duration": 1, "pid": 6212, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746257641205, "end": 1746257641206, "duration": 1, "pid": 6212, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746257641210, "end": 1746257641211, "duration": 1, "pid": 6212, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746257641212, "end": 1746257641213, "duration": 1, "pid": 6212, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746257641214, "end": 1746257641215, "duration": 1, "pid": 6212, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746257641216, "end": 1746257641217, "duration": 1, "pid": 6212, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746257641219, "end": 1746257641219, "duration": 0, "pid": 6212, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746257641220, "end": 1746257641221, "duration": 1, "pid": 6212, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746257641223, "end": 1746257641224, "duration": 1, "pid": 6212, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746257641226, "end": 1746257641226, "duration": 0, "pid": 6212, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746257641228, "end": 1746257641228, "duration": 0, "pid": 6212, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746257641230, "end": 1746257641230, "duration": 0, "pid": 6212, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746257641232, "end": 1746257641232, "duration": 0, "pid": 6212, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746257641233, "end": 1746257641234, "duration": 1, "pid": 6212, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746257641235, "end": 1746257641236, "duration": 1, "pid": 6212, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746257641238, "end": 1746257641241, "duration": 3, "pid": 6212, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746257641242, "end": 1746257641243, "duration": 1, "pid": 6212, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746257641245, "end": 1746257641245, "duration": 0, "pid": 6212, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746257641247, "end": 1746257641248, "duration": 1, "pid": 6212, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746257641251, "end": 1746257641252, "duration": 1, "pid": 6212, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746257641256, "end": 1746257641257, "duration": 1, "pid": 6212, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746257641262, "end": 1746257641262, "duration": 0, "pid": 6212, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746257641262, "end": 1746257641262, "duration": 0, "pid": 6212, "index": 30}, {"name": "Load extend/application.js", "start": 1746257641265, "end": 1746257641443, "duration": 178, "pid": 6212, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746257641266, "end": 1746257641267, "duration": 1, "pid": 6212, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746257641268, "end": 1746257641272, "duration": 4, "pid": 6212, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746257641273, "end": 1746257641283, "duration": 10, "pid": 6212, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746257641286, "end": 1746257641297, "duration": 11, "pid": 6212, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746257641300, "end": 1746257641304, "duration": 4, "pid": 6212, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746257641306, "end": 1746257641310, "duration": 4, "pid": 6212, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746257641312, "end": 1746257641427, "duration": 115, "pid": 6212, "index": 38}, {"name": "Load extend/request.js", "start": 1746257641443, "end": 1746257641471, "duration": 28, "pid": 6212, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746257641454, "end": 1746257641458, "duration": 4, "pid": 6212, "index": 40}, {"name": "Load extend/response.js", "start": 1746257641471, "end": 1746257641510, "duration": 39, "pid": 6212, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746257641491, "end": 1746257641498, "duration": 7, "pid": 6212, "index": 42}, {"name": "Load extend/context.js", "start": 1746257641510, "end": 1746257641670, "duration": 160, "pid": 6212, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746257641512, "end": 1746257641544, "duration": 32, "pid": 6212, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746257641545, "end": 1746257641549, "duration": 4, "pid": 6212, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746257641551, "end": 1746257641552, "duration": 1, "pid": 6212, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746257641554, "end": 1746257641609, "duration": 55, "pid": 6212, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746257641614, "end": 1746257641630, "duration": 16, "pid": 6212, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746257641636, "end": 1746257641637, "duration": 1, "pid": 6212, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746257641638, "end": 1746257641644, "duration": 6, "pid": 6212, "index": 50}, {"name": "Load extend/helper.js", "start": 1746257641670, "end": 1746257641768, "duration": 98, "pid": 6212, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746257641677, "end": 1746257641741, "duration": 64, "pid": 6212, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746257641752, "end": 1746257641753, "duration": 1, "pid": 6212, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746257641754, "end": 1746257641755, "duration": 1, "pid": 6212, "index": 54}, {"name": "Load app.js", "start": 1746257641768, "end": 1746257641898, "duration": 130, "pid": 6212, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746257641769, "end": 1746257641770, "duration": 1, "pid": 6212, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746257641771, "end": 1746257641776, "duration": 5, "pid": 6212, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746257641778, "end": 1746257641801, "duration": 23, "pid": 6212, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746257641802, "end": 1746257641823, "duration": 21, "pid": 6212, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746257641824, "end": 1746257641843, "duration": 19, "pid": 6212, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746257641844, "end": 1746257641845, "duration": 1, "pid": 6212, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746257641845, "end": 1746257641848, "duration": 3, "pid": 6212, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746257641848, "end": 1746257641849, "duration": 1, "pid": 6212, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746257641849, "end": 1746257641850, "duration": 1, "pid": 6212, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746257641850, "end": 1746257641851, "duration": 1, "pid": 6212, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746257641852, "end": 1746257641852, "duration": 0, "pid": 6212, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746257641853, "end": 1746257641854, "duration": 1, "pid": 6212, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746257641854, "end": 1746257641855, "duration": 1, "pid": 6212, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746257641855, "end": 1746257641864, "duration": 9, "pid": 6212, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746257641865, "end": 1746257641889, "duration": 24, "pid": 6212, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746257641890, "end": 1746257641896, "duration": 6, "pid": 6212, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746257641920, "end": 1746257647119, "duration": 5199, "pid": 6212, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746257644845, "end": 1746257645240, "duration": 395, "pid": 6212, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746257644947, "end": 1746257647024, "duration": 2077, "pid": 6212, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746257645318, "end": 1746257647150, "duration": 1832, "pid": 6212, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746257645594, "end": 1746257647124, "duration": 1530, "pid": 6212, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746257645844, "end": 1746257647061, "duration": 1217, "pid": 6212, "index": 77}, {"name": "Load Service", "start": 1746257645845, "end": 1746257646160, "duration": 315, "pid": 6212, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746257645845, "end": 1746257646160, "duration": 315, "pid": 6212, "index": 79}, {"name": "Load Middleware", "start": 1746257646161, "end": 1746257646589, "duration": 428, "pid": 6212, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746257646161, "end": 1746257646520, "duration": 359, "pid": 6212, "index": 81}, {"name": "Load Controller", "start": 1746257646589, "end": 1746257646726, "duration": 137, "pid": 6212, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746257646589, "end": 1746257646726, "duration": 137, "pid": 6212, "index": 83}, {"name": "Load Router", "start": 1746257646726, "end": 1746257646747, "duration": 21, "pid": 6212, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746257646727, "end": 1746257646728, "duration": 1, "pid": 6212, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746257646729, "end": 1746257647024, "duration": 295, "pid": 6212, "index": 86}]