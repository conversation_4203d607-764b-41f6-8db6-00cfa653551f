[{"name": "Process Start", "start": 1746261898276, "end": 1746261900595, "duration": 2319, "pid": 19032, "index": 0}, {"name": "Application Start", "start": 1746261900597, "end": 1746261903570, "duration": 2973, "pid": 19032, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746261900630, "end": 1746261900697, "duration": 67, "pid": 19032, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746261900697, "end": 1746261900765, "duration": 68, "pid": 19032, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746261900698, "end": 1746261900699, "duration": 1, "pid": 19032, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746261900704, "end": 1746261900706, "duration": 2, "pid": 19032, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746261900708, "end": 1746261900708, "duration": 0, "pid": 19032, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746261900710, "end": 1746261900711, "duration": 1, "pid": 19032, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746261900713, "end": 1746261900714, "duration": 1, "pid": 19032, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746261900714, "end": 1746261900717, "duration": 3, "pid": 19032, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746261900718, "end": 1746261900720, "duration": 2, "pid": 19032, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746261900721, "end": 1746261900722, "duration": 1, "pid": 19032, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746261900723, "end": 1746261900724, "duration": 1, "pid": 19032, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746261900725, "end": 1746261900726, "duration": 1, "pid": 19032, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746261900727, "end": 1746261900728, "duration": 1, "pid": 19032, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746261900729, "end": 1746261900730, "duration": 1, "pid": 19032, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746261900731, "end": 1746261900732, "duration": 1, "pid": 19032, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746261900734, "end": 1746261900735, "duration": 1, "pid": 19032, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746261900736, "end": 1746261900737, "duration": 1, "pid": 19032, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746261900737, "end": 1746261900738, "duration": 1, "pid": 19032, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746261900739, "end": 1746261900740, "duration": 1, "pid": 19032, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746261900740, "end": 1746261900741, "duration": 1, "pid": 19032, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746261900742, "end": 1746261900742, "duration": 0, "pid": 19032, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746261900743, "end": 1746261900744, "duration": 1, "pid": 19032, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746261900745, "end": 1746261900746, "duration": 1, "pid": 19032, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746261900749, "end": 1746261900749, "duration": 0, "pid": 19032, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746261900752, "end": 1746261900753, "duration": 1, "pid": 19032, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746261900755, "end": 1746261900756, "duration": 1, "pid": 19032, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746261900759, "end": 1746261900760, "duration": 1, "pid": 19032, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746261900764, "end": 1746261900764, "duration": 0, "pid": 19032, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746261900764, "end": 1746261900764, "duration": 0, "pid": 19032, "index": 30}, {"name": "Load extend/application.js", "start": 1746261900768, "end": 1746261900913, "duration": 145, "pid": 19032, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746261900769, "end": 1746261900770, "duration": 1, "pid": 19032, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746261900771, "end": 1746261900774, "duration": 3, "pid": 19032, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746261900775, "end": 1746261900784, "duration": 9, "pid": 19032, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746261900786, "end": 1746261900796, "duration": 10, "pid": 19032, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746261900798, "end": 1746261900802, "duration": 4, "pid": 19032, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746261900803, "end": 1746261900807, "duration": 4, "pid": 19032, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746261900809, "end": 1746261900903, "duration": 94, "pid": 19032, "index": 38}, {"name": "Load extend/request.js", "start": 1746261900913, "end": 1746261900932, "duration": 19, "pid": 19032, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746261900923, "end": 1746261900925, "duration": 2, "pid": 19032, "index": 40}, {"name": "Load extend/response.js", "start": 1746261900932, "end": 1746261900957, "duration": 25, "pid": 19032, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746261900943, "end": 1746261900948, "duration": 5, "pid": 19032, "index": 42}, {"name": "Load extend/context.js", "start": 1746261900957, "end": 1746261901057, "duration": 100, "pid": 19032, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746261900959, "end": 1746261900987, "duration": 28, "pid": 19032, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746261900987, "end": 1746261900991, "duration": 4, "pid": 19032, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746261900992, "end": 1746261900993, "duration": 1, "pid": 19032, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746261900995, "end": 1746261901038, "duration": 43, "pid": 19032, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746261901039, "end": 1746261901041, "duration": 2, "pid": 19032, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746261901043, "end": 1746261901044, "duration": 1, "pid": 19032, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746261901045, "end": 1746261901048, "duration": 3, "pid": 19032, "index": 50}, {"name": "Load extend/helper.js", "start": 1746261901057, "end": 1746261901114, "duration": 57, "pid": 19032, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746261901059, "end": 1746261901094, "duration": 35, "pid": 19032, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746261901103, "end": 1746261901103, "duration": 0, "pid": 19032, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746261901104, "end": 1746261901105, "duration": 1, "pid": 19032, "index": 54}, {"name": "Load app.js", "start": 1746261901114, "end": 1746261901248, "duration": 134, "pid": 19032, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746261901115, "end": 1746261901115, "duration": 0, "pid": 19032, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746261901117, "end": 1746261901121, "duration": 4, "pid": 19032, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746261901122, "end": 1746261901143, "duration": 21, "pid": 19032, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746261901143, "end": 1746261901163, "duration": 20, "pid": 19032, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746261901164, "end": 1746261901184, "duration": 20, "pid": 19032, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746261901185, "end": 1746261901187, "duration": 2, "pid": 19032, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746261901187, "end": 1746261901190, "duration": 3, "pid": 19032, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746261901191, "end": 1746261901191, "duration": 0, "pid": 19032, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746261901192, "end": 1746261901193, "duration": 1, "pid": 19032, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746261901193, "end": 1746261901194, "duration": 1, "pid": 19032, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746261901195, "end": 1746261901196, "duration": 1, "pid": 19032, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746261901196, "end": 1746261901197, "duration": 1, "pid": 19032, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746261901198, "end": 1746261901198, "duration": 0, "pid": 19032, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746261901199, "end": 1746261901203, "duration": 4, "pid": 19032, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746261901204, "end": 1746261901235, "duration": 31, "pid": 19032, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746261901236, "end": 1746261901246, "duration": 10, "pid": 19032, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746261901268, "end": 1746261903552, "duration": 2284, "pid": 19032, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746261902148, "end": 1746261902357, "duration": 209, "pid": 19032, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746261902197, "end": 1746261903483, "duration": 1286, "pid": 19032, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746261902422, "end": 1746261903569, "duration": 1147, "pid": 19032, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746261902541, "end": 1746261903553, "duration": 1012, "pid": 19032, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746261902695, "end": 1746261903509, "duration": 814, "pid": 19032, "index": 77}, {"name": "Load Service", "start": 1746261902695, "end": 1746261902944, "duration": 249, "pid": 19032, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746261902696, "end": 1746261902944, "duration": 248, "pid": 19032, "index": 79}, {"name": "Load Middleware", "start": 1746261902945, "end": 1746261903168, "duration": 223, "pid": 19032, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746261902945, "end": 1746261903147, "duration": 202, "pid": 19032, "index": 81}, {"name": "Load Controller", "start": 1746261903168, "end": 1746261903321, "duration": 153, "pid": 19032, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746261903168, "end": 1746261903321, "duration": 153, "pid": 19032, "index": 83}, {"name": "Load Router", "start": 1746261903321, "end": 1746261903348, "duration": 27, "pid": 19032, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746261903321, "end": 1746261903324, "duration": 3, "pid": 19032, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746261903330, "end": 1746261903483, "duration": 153, "pid": 19032, "index": 86}]