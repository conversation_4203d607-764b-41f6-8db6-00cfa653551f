[{"name": "Process Start", "start": 1746258063918, "end": 1746258066806, "duration": 2888, "pid": 23740, "index": 0}, {"name": "Application Start", "start": 1746258066809, "end": 1746258069792, "duration": 2983, "pid": 23740, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746258066846, "end": 1746258066900, "duration": 54, "pid": 23740, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746258066900, "end": 1746258067009, "duration": 109, "pid": 23740, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746258066902, "end": 1746258066903, "duration": 1, "pid": 23740, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746258066905, "end": 1746258066906, "duration": 1, "pid": 23740, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746258066907, "end": 1746258066908, "duration": 1, "pid": 23740, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746258066909, "end": 1746258066910, "duration": 1, "pid": 23740, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746258066914, "end": 1746258066915, "duration": 1, "pid": 23740, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746258066916, "end": 1746258066917, "duration": 1, "pid": 23740, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746258066919, "end": 1746258066921, "duration": 2, "pid": 23740, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746258066922, "end": 1746258066923, "duration": 1, "pid": 23740, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746258066924, "end": 1746258066925, "duration": 1, "pid": 23740, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746258066927, "end": 1746258066929, "duration": 2, "pid": 23740, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746258066931, "end": 1746258066932, "duration": 1, "pid": 23740, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746258066933, "end": 1746258066934, "duration": 1, "pid": 23740, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746258066935, "end": 1746258066937, "duration": 2, "pid": 23740, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746258066938, "end": 1746258066939, "duration": 1, "pid": 23740, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746258066941, "end": 1746258066942, "duration": 1, "pid": 23740, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746258066943, "end": 1746258066947, "duration": 4, "pid": 23740, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746258066949, "end": 1746258066949, "duration": 0, "pid": 23740, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746258066951, "end": 1746258066952, "duration": 1, "pid": 23740, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746258066953, "end": 1746258066954, "duration": 1, "pid": 23740, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746258066958, "end": 1746258066964, "duration": 6, "pid": 23740, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746258066966, "end": 1746258066970, "duration": 4, "pid": 23740, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746258066973, "end": 1746258066973, "duration": 0, "pid": 23740, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746258066976, "end": 1746258066979, "duration": 3, "pid": 23740, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746258066983, "end": 1746258066983, "duration": 0, "pid": 23740, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746258066988, "end": 1746258066989, "duration": 1, "pid": 23740, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746258067007, "end": 1746258067008, "duration": 1, "pid": 23740, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746258067009, "end": 1746258067009, "duration": 0, "pid": 23740, "index": 30}, {"name": "Load extend/application.js", "start": 1746258067014, "end": 1746258067235, "duration": 221, "pid": 23740, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746258067016, "end": 1746258067023, "duration": 7, "pid": 23740, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746258067025, "end": 1746258067034, "duration": 9, "pid": 23740, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746258067036, "end": 1746258067051, "duration": 15, "pid": 23740, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746258067055, "end": 1746258067072, "duration": 17, "pid": 23740, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746258067074, "end": 1746258067082, "duration": 8, "pid": 23740, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746258067085, "end": 1746258067090, "duration": 5, "pid": 23740, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746258067091, "end": 1746258067219, "duration": 128, "pid": 23740, "index": 38}, {"name": "Load extend/request.js", "start": 1746258067235, "end": 1746258067263, "duration": 28, "pid": 23740, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746258067250, "end": 1746258067253, "duration": 3, "pid": 23740, "index": 40}, {"name": "Load extend/response.js", "start": 1746258067263, "end": 1746258067286, "duration": 23, "pid": 23740, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746258067273, "end": 1746258067278, "duration": 5, "pid": 23740, "index": 42}, {"name": "Load extend/context.js", "start": 1746258067286, "end": 1746258067397, "duration": 111, "pid": 23740, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746258067287, "end": 1746258067315, "duration": 28, "pid": 23740, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746258067316, "end": 1746258067321, "duration": 5, "pid": 23740, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746258067323, "end": 1746258067324, "duration": 1, "pid": 23740, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746258067325, "end": 1746258067376, "duration": 51, "pid": 23740, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746258067379, "end": 1746258067381, "duration": 2, "pid": 23740, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746258067383, "end": 1746258067384, "duration": 1, "pid": 23740, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746258067385, "end": 1746258067389, "duration": 4, "pid": 23740, "index": 50}, {"name": "Load extend/helper.js", "start": 1746258067397, "end": 1746258067455, "duration": 58, "pid": 23740, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746258067398, "end": 1746258067435, "duration": 37, "pid": 23740, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746258067441, "end": 1746258067442, "duration": 1, "pid": 23740, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746258067443, "end": 1746258067444, "duration": 1, "pid": 23740, "index": 54}, {"name": "Load app.js", "start": 1746258067455, "end": 1746258067586, "duration": 131, "pid": 23740, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746258067456, "end": 1746258067457, "duration": 1, "pid": 23740, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746258067458, "end": 1746258067463, "duration": 5, "pid": 23740, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746258067464, "end": 1746258067487, "duration": 23, "pid": 23740, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746258067488, "end": 1746258067514, "duration": 26, "pid": 23740, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746258067514, "end": 1746258067535, "duration": 21, "pid": 23740, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746258067535, "end": 1746258067537, "duration": 2, "pid": 23740, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746258067538, "end": 1746258067540, "duration": 2, "pid": 23740, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746258067541, "end": 1746258067542, "duration": 1, "pid": 23740, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746258067542, "end": 1746258067543, "duration": 1, "pid": 23740, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746258067544, "end": 1746258067544, "duration": 0, "pid": 23740, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746258067546, "end": 1746258067547, "duration": 1, "pid": 23740, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746258067547, "end": 1746258067548, "duration": 1, "pid": 23740, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746258067548, "end": 1746258067549, "duration": 1, "pid": 23740, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746258067549, "end": 1746258067552, "duration": 3, "pid": 23740, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746258067553, "end": 1746258067575, "duration": 22, "pid": 23740, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746258067575, "end": 1746258067584, "duration": 9, "pid": 23740, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746258067603, "end": 1746258069762, "duration": 2159, "pid": 23740, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746258068604, "end": 1746258068709, "duration": 105, "pid": 23740, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746258068638, "end": 1746258069647, "duration": 1009, "pid": 23740, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746258068750, "end": 1746258069791, "duration": 1041, "pid": 23740, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746258068900, "end": 1746258069764, "duration": 864, "pid": 23740, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746258069049, "end": 1746258069685, "duration": 636, "pid": 23740, "index": 77}, {"name": "Load Service", "start": 1746258069049, "end": 1746258069238, "duration": 189, "pid": 23740, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746258069050, "end": 1746258069238, "duration": 188, "pid": 23740, "index": 79}, {"name": "Load Middleware", "start": 1746258069238, "end": 1746258069508, "duration": 270, "pid": 23740, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746258069238, "end": 1746258069488, "duration": 250, "pid": 23740, "index": 81}, {"name": "Load Controller", "start": 1746258069508, "end": 1746258069555, "duration": 47, "pid": 23740, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746258069508, "end": 1746258069555, "duration": 47, "pid": 23740, "index": 83}, {"name": "Load Router", "start": 1746258069555, "end": 1746258069569, "duration": 14, "pid": 23740, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746258069556, "end": 1746258069557, "duration": 1, "pid": 23740, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746258069558, "end": 1746258069647, "duration": 89, "pid": 23740, "index": 86}]