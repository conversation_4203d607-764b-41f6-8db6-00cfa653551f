[{"name": "Process Start", "start": 1746258675054, "end": 1746258677059, "duration": 2005, "pid": 23376, "index": 0}, {"name": "Application Start", "start": 1746258677061, "end": 1746258679092, "duration": 2031, "pid": 23376, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746258677082, "end": 1746258677122, "duration": 40, "pid": 23376, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746258677122, "end": 1746258677166, "duration": 44, "pid": 23376, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746258677123, "end": 1746258677123, "duration": 0, "pid": 23376, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746258677125, "end": 1746258677126, "duration": 1, "pid": 23376, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746258677127, "end": 1746258677127, "duration": 0, "pid": 23376, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746258677128, "end": 1746258677129, "duration": 1, "pid": 23376, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746258677131, "end": 1746258677132, "duration": 1, "pid": 23376, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746258677133, "end": 1746258677133, "duration": 0, "pid": 23376, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746258677134, "end": 1746258677134, "duration": 0, "pid": 23376, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746258677135, "end": 1746258677136, "duration": 1, "pid": 23376, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746258677136, "end": 1746258677137, "duration": 1, "pid": 23376, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746258677138, "end": 1746258677138, "duration": 0, "pid": 23376, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746258677139, "end": 1746258677140, "duration": 1, "pid": 23376, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746258677140, "end": 1746258677141, "duration": 1, "pid": 23376, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746258677141, "end": 1746258677142, "duration": 1, "pid": 23376, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746258677143, "end": 1746258677143, "duration": 0, "pid": 23376, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746258677144, "end": 1746258677144, "duration": 0, "pid": 23376, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746258677145, "end": 1746258677146, "duration": 1, "pid": 23376, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746258677147, "end": 1746258677148, "duration": 1, "pid": 23376, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746258677148, "end": 1746258677149, "duration": 1, "pid": 23376, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746258677150, "end": 1746258677151, "duration": 1, "pid": 23376, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746258677151, "end": 1746258677152, "duration": 1, "pid": 23376, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746258677152, "end": 1746258677153, "duration": 1, "pid": 23376, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746258677154, "end": 1746258677154, "duration": 0, "pid": 23376, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746258677156, "end": 1746258677156, "duration": 0, "pid": 23376, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746258677157, "end": 1746258677158, "duration": 1, "pid": 23376, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746258677160, "end": 1746258677161, "duration": 1, "pid": 23376, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746258677166, "end": 1746258677166, "duration": 0, "pid": 23376, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746258677166, "end": 1746258677166, "duration": 0, "pid": 23376, "index": 30}, {"name": "Load extend/application.js", "start": 1746258677167, "end": 1746258677266, "duration": 99, "pid": 23376, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746258677168, "end": 1746258677169, "duration": 1, "pid": 23376, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746258677170, "end": 1746258677172, "duration": 2, "pid": 23376, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746258677172, "end": 1746258677180, "duration": 8, "pid": 23376, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746258677182, "end": 1746258677189, "duration": 7, "pid": 23376, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746258677190, "end": 1746258677192, "duration": 2, "pid": 23376, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746258677193, "end": 1746258677197, "duration": 4, "pid": 23376, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746258677198, "end": 1746258677257, "duration": 59, "pid": 23376, "index": 38}, {"name": "Load extend/request.js", "start": 1746258677266, "end": 1746258677282, "duration": 16, "pid": 23376, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746258677273, "end": 1746258677275, "duration": 2, "pid": 23376, "index": 40}, {"name": "Load extend/response.js", "start": 1746258677282, "end": 1746258677299, "duration": 17, "pid": 23376, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746258677289, "end": 1746258677292, "duration": 3, "pid": 23376, "index": 42}, {"name": "Load extend/context.js", "start": 1746258677299, "end": 1746258677368, "duration": 69, "pid": 23376, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746258677300, "end": 1746258677318, "duration": 18, "pid": 23376, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746258677319, "end": 1746258677322, "duration": 3, "pid": 23376, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746258677323, "end": 1746258677323, "duration": 0, "pid": 23376, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746258677325, "end": 1746258677352, "duration": 27, "pid": 23376, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746258677354, "end": 1746258677355, "duration": 1, "pid": 23376, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746258677357, "end": 1746258677357, "duration": 0, "pid": 23376, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746258677358, "end": 1746258677362, "duration": 4, "pid": 23376, "index": 50}, {"name": "Load extend/helper.js", "start": 1746258677368, "end": 1746258677408, "duration": 40, "pid": 23376, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746258677369, "end": 1746258677393, "duration": 24, "pid": 23376, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746258677399, "end": 1746258677400, "duration": 1, "pid": 23376, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746258677400, "end": 1746258677401, "duration": 1, "pid": 23376, "index": 54}, {"name": "Load app.js", "start": 1746258677408, "end": 1746258677506, "duration": 98, "pid": 23376, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746258677409, "end": 1746258677409, "duration": 0, "pid": 23376, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746258677410, "end": 1746258677413, "duration": 3, "pid": 23376, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746258677415, "end": 1746258677432, "duration": 17, "pid": 23376, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746258677433, "end": 1746258677449, "duration": 16, "pid": 23376, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746258677449, "end": 1746258677464, "duration": 15, "pid": 23376, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746258677464, "end": 1746258677465, "duration": 1, "pid": 23376, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746258677466, "end": 1746258677468, "duration": 2, "pid": 23376, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746258677468, "end": 1746258677469, "duration": 1, "pid": 23376, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746258677469, "end": 1746258677470, "duration": 1, "pid": 23376, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746258677470, "end": 1746258677471, "duration": 1, "pid": 23376, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746258677472, "end": 1746258677472, "duration": 0, "pid": 23376, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746258677473, "end": 1746258677473, "duration": 0, "pid": 23376, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746258677474, "end": 1746258677474, "duration": 0, "pid": 23376, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746258677475, "end": 1746258677477, "duration": 2, "pid": 23376, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746258677478, "end": 1746258677497, "duration": 19, "pid": 23376, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746258677498, "end": 1746258677503, "duration": 5, "pid": 23376, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746258677519, "end": 1746258679074, "duration": 1555, "pid": 23376, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746258678263, "end": 1746258678346, "duration": 83, "pid": 23376, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746258678287, "end": 1746258679027, "duration": 740, "pid": 23376, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746258678374, "end": 1746258679091, "duration": 717, "pid": 23376, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746258678469, "end": 1746258679076, "duration": 607, "pid": 23376, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746258678575, "end": 1746258679046, "duration": 471, "pid": 23376, "index": 77}, {"name": "Load Service", "start": 1746258678576, "end": 1746258678719, "duration": 143, "pid": 23376, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746258678576, "end": 1746258678719, "duration": 143, "pid": 23376, "index": 79}, {"name": "Load Middleware", "start": 1746258678719, "end": 1746258678912, "duration": 193, "pid": 23376, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746258678719, "end": 1746258678897, "duration": 178, "pid": 23376, "index": 81}, {"name": "Load Controller", "start": 1746258678912, "end": 1746258678954, "duration": 42, "pid": 23376, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746258678912, "end": 1746258678954, "duration": 42, "pid": 23376, "index": 83}, {"name": "Load Router", "start": 1746258678954, "end": 1746258678961, "duration": 7, "pid": 23376, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746258678955, "end": 1746258678956, "duration": 1, "pid": 23376, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746258678956, "end": 1746258679027, "duration": 71, "pid": 23376, "index": 86}]