[{"name": "Process Start", "start": 1746262549596, "end": 1746262551538, "duration": 1942, "pid": 8848, "index": 0}, {"name": "Application Start", "start": 1746262551539, "end": 1746262553855, "duration": 2316, "pid": 8848, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746262551562, "end": 1746262551599, "duration": 37, "pid": 8848, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746262551599, "end": 1746262551648, "duration": 49, "pid": 8848, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746262551600, "end": 1746262551600, "duration": 0, "pid": 8848, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746262551602, "end": 1746262551603, "duration": 1, "pid": 8848, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746262551603, "end": 1746262551604, "duration": 1, "pid": 8848, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746262551605, "end": 1746262551606, "duration": 1, "pid": 8848, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746262551612, "end": 1746262551613, "duration": 1, "pid": 8848, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746262551614, "end": 1746262551614, "duration": 0, "pid": 8848, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746262551615, "end": 1746262551615, "duration": 0, "pid": 8848, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746262551616, "end": 1746262551616, "duration": 0, "pid": 8848, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746262551617, "end": 1746262551618, "duration": 1, "pid": 8848, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746262551618, "end": 1746262551619, "duration": 1, "pid": 8848, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746262551620, "end": 1746262551620, "duration": 0, "pid": 8848, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746262551621, "end": 1746262551622, "duration": 1, "pid": 8848, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746262551622, "end": 1746262551623, "duration": 1, "pid": 8848, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746262551623, "end": 1746262551624, "duration": 1, "pid": 8848, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746262551626, "end": 1746262551627, "duration": 1, "pid": 8848, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746262551628, "end": 1746262551628, "duration": 0, "pid": 8848, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746262551629, "end": 1746262551630, "duration": 1, "pid": 8848, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746262551631, "end": 1746262551631, "duration": 0, "pid": 8848, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746262551632, "end": 1746262551633, "duration": 1, "pid": 8848, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746262551633, "end": 1746262551634, "duration": 1, "pid": 8848, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746262551634, "end": 1746262551635, "duration": 1, "pid": 8848, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746262551637, "end": 1746262551637, "duration": 0, "pid": 8848, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746262551638, "end": 1746262551638, "duration": 0, "pid": 8848, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746262551640, "end": 1746262551640, "duration": 0, "pid": 8848, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746262551644, "end": 1746262551644, "duration": 0, "pid": 8848, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746262551648, "end": 1746262551648, "duration": 0, "pid": 8848, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746262551648, "end": 1746262551648, "duration": 0, "pid": 8848, "index": 30}, {"name": "Load extend/application.js", "start": 1746262551650, "end": 1746262551783, "duration": 133, "pid": 8848, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746262551651, "end": 1746262551652, "duration": 1, "pid": 8848, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746262551653, "end": 1746262551655, "duration": 2, "pid": 8848, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746262551656, "end": 1746262551663, "duration": 7, "pid": 8848, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746262551665, "end": 1746262551672, "duration": 7, "pid": 8848, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746262551674, "end": 1746262551677, "duration": 3, "pid": 8848, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746262551678, "end": 1746262551680, "duration": 2, "pid": 8848, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746262551681, "end": 1746262551770, "duration": 89, "pid": 8848, "index": 38}, {"name": "Load extend/request.js", "start": 1746262551783, "end": 1746262551799, "duration": 16, "pid": 8848, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746262551789, "end": 1746262551792, "duration": 3, "pid": 8848, "index": 40}, {"name": "Load extend/response.js", "start": 1746262551799, "end": 1746262551820, "duration": 21, "pid": 8848, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746262551807, "end": 1746262551811, "duration": 4, "pid": 8848, "index": 42}, {"name": "Load extend/context.js", "start": 1746262551820, "end": 1746262551897, "duration": 77, "pid": 8848, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746262551821, "end": 1746262551843, "duration": 22, "pid": 8848, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746262551843, "end": 1746262551846, "duration": 3, "pid": 8848, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746262551847, "end": 1746262551848, "duration": 1, "pid": 8848, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746262551850, "end": 1746262551880, "duration": 30, "pid": 8848, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746262551881, "end": 1746262551883, "duration": 2, "pid": 8848, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746262551884, "end": 1746262551885, "duration": 1, "pid": 8848, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746262551887, "end": 1746262551890, "duration": 3, "pid": 8848, "index": 50}, {"name": "Load extend/helper.js", "start": 1746262551897, "end": 1746262551956, "duration": 59, "pid": 8848, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746262551898, "end": 1746262551935, "duration": 37, "pid": 8848, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746262551942, "end": 1746262551943, "duration": 1, "pid": 8848, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746262551944, "end": 1746262551945, "duration": 1, "pid": 8848, "index": 54}, {"name": "Load app.js", "start": 1746262551957, "end": 1746262552092, "duration": 135, "pid": 8848, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746262551957, "end": 1746262551959, "duration": 2, "pid": 8848, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746262551960, "end": 1746262551964, "duration": 4, "pid": 8848, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746262551966, "end": 1746262551984, "duration": 18, "pid": 8848, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746262551985, "end": 1746262552002, "duration": 17, "pid": 8848, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746262552003, "end": 1746262552020, "duration": 17, "pid": 8848, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746262552021, "end": 1746262552022, "duration": 1, "pid": 8848, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746262552023, "end": 1746262552027, "duration": 4, "pid": 8848, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746262552027, "end": 1746262552028, "duration": 1, "pid": 8848, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746262552030, "end": 1746262552030, "duration": 0, "pid": 8848, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746262552031, "end": 1746262552031, "duration": 0, "pid": 8848, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746262552033, "end": 1746262552034, "duration": 1, "pid": 8848, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746262552034, "end": 1746262552035, "duration": 1, "pid": 8848, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746262552036, "end": 1746262552036, "duration": 0, "pid": 8848, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746262552037, "end": 1746262552044, "duration": 7, "pid": 8848, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746262552044, "end": 1746262552080, "duration": 36, "pid": 8848, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746262552081, "end": 1746262552090, "duration": 9, "pid": 8848, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746262552109, "end": 1746262553831, "duration": 1722, "pid": 8848, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746262552946, "end": 1746262553043, "duration": 97, "pid": 8848, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746262552972, "end": 1746262553762, "duration": 790, "pid": 8848, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746262553077, "end": 1746262553854, "duration": 777, "pid": 8848, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746262553182, "end": 1746262553833, "duration": 651, "pid": 8848, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746262553314, "end": 1746262553793, "duration": 479, "pid": 8848, "index": 77}, {"name": "Load Service", "start": 1746262553314, "end": 1746262553436, "duration": 122, "pid": 8848, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746262553314, "end": 1746262553436, "duration": 122, "pid": 8848, "index": 79}, {"name": "Load Middleware", "start": 1746262553436, "end": 1746262553614, "duration": 178, "pid": 8848, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746262553437, "end": 1746262553596, "duration": 159, "pid": 8848, "index": 81}, {"name": "Load Controller", "start": 1746262553614, "end": 1746262553670, "duration": 56, "pid": 8848, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746262553614, "end": 1746262553670, "duration": 56, "pid": 8848, "index": 83}, {"name": "Load Router", "start": 1746262553670, "end": 1746262553678, "duration": 8, "pid": 8848, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746262553670, "end": 1746262553671, "duration": 1, "pid": 8848, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746262553673, "end": 1746262553762, "duration": 89, "pid": 8848, "index": 86}]