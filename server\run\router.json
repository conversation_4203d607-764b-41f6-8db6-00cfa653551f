[{"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/", "regexp": "/^(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/auth/register", "regexp": "/^\\/api\\/auth\\/register(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/auth/login", "regexp": "/^\\/api\\/auth\\/login(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/auth/password-reset-request", "regexp": "/^\\/api\\/auth\\/password-reset-request(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/auth/validate-token", "regexp": "/^\\/api\\/auth\\/validate-token(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/users/profile", "regexp": "/^\\/api\\/users\\/profile(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [], "path": "/api/users/profile", "regexp": "/^\\/api\\/users\\/profile(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [], "path": "/api/users/preferences", "regexp": "/^\\/api\\/users\\/preferences(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [], "path": "/api/users/password", "regexp": "/^\\/api\\/users\\/password(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/watchlists", "regexp": "/^\\/api\\/watchlists(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/watchlists", "regexp": "/^\\/api\\/watchlists(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlists/:id", "regexp": "/^\\/api\\/watchlists\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlists/:id", "regexp": "/^\\/api\\/watchlists\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlists/:id/stocks", "regexp": "/^\\/api\\/watchlists\\/((?:[^\\/]+?))\\/stocks(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlists/:id/stocks", "regexp": "/^\\/api\\/watchlists\\/((?:[^\\/]+?))\\/stocks(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "watchlistId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "itemId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlists/:watchlistId/stocks/:itemId", "regexp": "/^\\/api\\/watchlists\\/((?:[^\\/]+?))\\/stocks\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "watchlistId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "itemId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlists/:watchlistId/stocks/:itemId/notes", "regexp": "/^\\/api\\/watchlists\\/((?:[^\\/]+?))\\/stocks\\/((?:[^\\/]+?))\\/notes(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/portfolios", "regexp": "/^\\/api\\/portfolios(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/portfolios", "regexp": "/^\\/api\\/portfolios(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:id", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:id", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:id/holdings", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/holdings(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:id/holdings", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/holdings(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "portfolioId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "holdingId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:portfolioId/holdings/:holdingId", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/holdings\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "portfolioId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "holdingId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:portfolioId/holdings/:holdingId", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/holdings\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:id/trades", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/trades(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:id/trades", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/trades(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "portfolioId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "tradeId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:portfolioId/trades/:tradeId", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/trades\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/simulation/accounts", "regexp": "/^\\/api\\/simulation\\/accounts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/simulation/accounts", "regexp": "/^\\/api\\/simulation\\/accounts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/simulation/accounts/:id", "regexp": "/^\\/api\\/simulation\\/accounts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/simulation/accounts/:id/positions", "regexp": "/^\\/api\\/simulation\\/accounts\\/((?:[^\\/]+?))\\/positions(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/simulation/accounts/:id/transactions", "regexp": "/^\\/api\\/simulation\\/accounts\\/((?:[^\\/]+?))\\/transactions(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/simulation/accounts/:id/trade", "regexp": "/^\\/api\\/simulation\\/accounts\\/((?:[^\\/]+?))\\/trade(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stocks", "regexp": "/^\\/api\\/stocks(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "code", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/stocks/:code/quote", "regexp": "/^\\/api\\/stocks\\/((?:[^\\/]+?))\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "code", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/stocks/:code/history", "regexp": "/^\\/api\\/stocks\\/((?:[^\\/]+?))\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/test/redis", "regexp": "/^\\/api\\/test\\/redis(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/test/store-stock", "regexp": "/^\\/api\\/test\\/store-stock(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/test/store-all-stocks", "regexp": "/^\\/api\\/test\\/store-all-stocks(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/env/info", "regexp": "/^\\/api\\/env\\/info(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/sina/test", "regexp": "/^\\/api\\/sina\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/sina/quote", "regexp": "/^\\/api\\/sina\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/sina/stock-list", "regexp": "/^\\/api\\/sina\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/sina/search", "regexp": "/^\\/api\\/sina\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/sina/history", "regexp": "/^\\/api\\/sina\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/sina/news", "regexp": "/^\\/api\\/sina\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/eastmoney/test", "regexp": "/^\\/api\\/eastmoney\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/eastmoney/quote", "regexp": "/^\\/api\\/eastmoney\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/eastmoney/stock-list", "regexp": "/^\\/api\\/eastmoney\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/eastmoney/search", "regexp": "/^\\/api\\/eastmoney\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/eastmoney/history", "regexp": "/^\\/api\\/eastmoney\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/eastmoney/news", "regexp": "/^\\/api\\/eastmoney\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tencent/test", "regexp": "/^\\/api\\/tencent\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tencent/quote", "regexp": "/^\\/api\\/tencent\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tencent/stock-list", "regexp": "/^\\/api\\/tencent\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tencent/search", "regexp": "/^\\/api\\/tencent\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tencent/history", "regexp": "/^\\/api\\/tencent\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tencent/news", "regexp": "/^\\/api\\/tencent\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/netease/test", "regexp": "/^\\/api\\/netease\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/netease/quote", "regexp": "/^\\/api\\/netease\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/netease/stock-list", "regexp": "/^\\/api\\/netease\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/netease/search", "regexp": "/^\\/api\\/netease\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/netease/history", "regexp": "/^\\/api\\/netease\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/netease/news", "regexp": "/^\\/api\\/netease\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/akshare/test", "regexp": "/^\\/api\\/akshare\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/akshare/quote", "regexp": "/^\\/api\\/akshare\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/akshare/stock-list", "regexp": "/^\\/api\\/akshare\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/akshare/search", "regexp": "/^\\/api\\/akshare\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/akshare/history", "regexp": "/^\\/api\\/akshare\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/akshare/news", "regexp": "/^\\/api\\/akshare\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tushare/test", "regexp": "/^\\/api\\/tushare\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tushare/stock-basic", "regexp": "/^\\/api\\/tushare\\/stock-basic(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/tushare/update-stock-basic", "regexp": "/^\\/api\\/tushare\\/update-stock-basic(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/tushare", "regexp": "/^\\/api\\/tushare(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/data-source/test", "regexp": "/^\\/api\\/data-source\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/data-source/stocks", "regexp": "/^\\/api\\/data-source\\/stocks(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/alerts", "regexp": "/^\\/api\\/alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/alerts", "regexp": "/^\\/api\\/alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PATCH"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/alerts/:id", "regexp": "/^\\/api\\/alerts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/alerts/:id", "regexp": "/^\\/api\\/alerts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/alerts/:id/history", "regexp": "/^\\/api\\/alerts\\/((?:[^\\/]+?))\\/history(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PATCH"], "paramNames": [{"name": "historyId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/alerts/history/:historyId", "regexp": "/^\\/api\\/alerts\\/history\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/watchlist-alerts", "regexp": "/^\\/api\\/watchlist-alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/watchlist-alerts", "regexp": "/^\\/api\\/watchlist-alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "watchlistId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "alertId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlist-alerts/:watchlistId/:alertId", "regexp": "/^\\/api\\/watchlist-alerts\\/((?:[^\\/]+?))\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/cache/status", "regexp": "/^\\/api\\/cache\\/status(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/cache/refresh", "regexp": "/^\\/api\\/cache\\/refresh(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "dataSource", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/cache/:dataSource", "regexp": "/^\\/api\\/cache\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "dataSource", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/cache/source/:dataSource", "regexp": "/^\\/api\\/cache\\/source\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/cache/refresh-limit", "regexp": "/^\\/api\\/cache\\/refresh-limit(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/cache-stats", "regexp": "/^\\/api\\/cache-stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/cache-stats/reset", "regexp": "/^\\/api\\/cache-stats\\/reset(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/refresh-data", "regexp": "/^\\/api\\/refresh-data(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/refresh-status", "regexp": "/^\\/api\\/refresh-status(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/membership", "regexp": "/^\\/api\\/membership(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/membership/levels", "regexp": "/^\\/api\\/membership\\/levels(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/membership/check-access", "regexp": "/^\\/api\\/membership\\/check-access(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/membership/update", "regexp": "/^\\/api\\/membership\\/update(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/coins", "regexp": "/^\\/api\\/coins(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/coins/transactions", "regexp": "/^\\/api\\/coins\\/transactions(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/coins/exchange", "regexp": "/^\\/api\\/coins\\/exchange(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/coins/add", "regexp": "/^\\/api\\/coins\\/add(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/coins/deduct", "regexp": "/^\\/api\\/coins\\/deduct(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/coins/recharge", "regexp": "/^\\/api\\/coins\\/recharge(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/coins/recharge", "regexp": "/^\\/api\\/coins\\/recharge(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/coins/recharge/all", "regexp": "/^\\/api\\/coins\\/recharge\\/all(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "requestId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/coins/recharge/:requestId", "regexp": "/^\\/api\\/coins\\/recharge\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "requestId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/coins/recharge/:requestId/process", "regexp": "/^\\/api\\/coins\\/recharge\\/((?:[^\\/]+?))\\/process(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "requestId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/coins/recharge/:requestId/cancel", "regexp": "/^\\/api\\/coins\\/recharge\\/((?:[^\\/]+?))\\/cancel(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/notifications", "regexp": "/^\\/api\\/notifications(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/notifications/unread-count", "regexp": "/^\\/api\\/notifications\\/unread-count(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "notificationId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/notifications/:notificationId/read", "regexp": "/^\\/api\\/notifications\\/((?:[^\\/]+?))\\/read(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/notifications/read-all", "regexp": "/^\\/api\\/notifications\\/read-all(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "notificationId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/notifications/:notificationId", "regexp": "/^\\/api\\/notifications\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/admin/users", "regexp": "/^\\/api\\/admin\\/users(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "userId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/admin/users/:userId", "regexp": "/^\\/api\\/admin\\/users\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "userId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/admin/users/:userId", "regexp": "/^\\/api\\/admin\\/users\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PATCH"], "paramNames": [{"name": "userId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/admin/users/:userId/status", "regexp": "/^\\/api\\/admin\\/users\\/((?:[^\\/]+?))\\/status(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/admin/stats", "regexp": "/^\\/api\\/admin\\/stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/pages", "regexp": "/^\\/api\\/pages(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/pages/:id", "regexp": "/^\\/api\\/pages\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/pages", "regexp": "/^\\/api\\/pages(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/pages/:id", "regexp": "/^\\/api\\/pages\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/pages/:id", "regexp": "/^\\/api\\/pages\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/pages/:id/permissions", "regexp": "/^\\/api\\/pages\\/((?:[^\\/]+?))\\/permissions(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/pages/batch-status", "regexp": "/^\\/api\\/pages\\/batch-status(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/pages/init", "regexp": "/^\\/api\\/pages\\/init(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/user-menu", "regexp": "/^\\/api\\/user-menu(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/check-page-access", "regexp": "/^\\/api\\/check-page-access(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/page-stats/summary", "regexp": "/^\\/api\\/page-stats\\/summary(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/page-stats/stats", "regexp": "/^\\/api\\/page-stats\\/stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/page-stats/logs", "regexp": "/^\\/api\\/page-stats\\/logs(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/page-stats/log", "regexp": "/^\\/api\\/page-stats\\/log(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/page-stats/duration", "regexp": "/^\\/api\\/page-stats\\/duration(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/page-groups", "regexp": "/^\\/api\\/page-groups(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/page-groups/:id", "regexp": "/^\\/api\\/page-groups\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/page-groups", "regexp": "/^\\/api\\/page-groups(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/page-groups/:id", "regexp": "/^\\/api\\/page-groups\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/page-groups/:id", "regexp": "/^\\/api\\/page-groups\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/page-groups/:id/permissions", "regexp": "/^\\/api\\/page-groups\\/((?:[^\\/]+?))\\/permissions(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/permission-templates", "regexp": "/^\\/api\\/permission-templates(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/permission-templates/:id", "regexp": "/^\\/api\\/permission-templates\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/permission-templates", "regexp": "/^\\/api\\/permission-templates(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/permission-templates/:id", "regexp": "/^\\/api\\/permission-templates\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/permission-templates/:id", "regexp": "/^\\/api\\/permission-templates\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/permission-templates/apply-to-page", "regexp": "/^\\/api\\/permission-templates\\/apply-to-page(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/permission-templates/apply-to-group", "regexp": "/^\\/api\\/permission-templates\\/apply-to-group(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/logs/data-source", "regexp": "/^\\/api\\/logs\\/data-source(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/logs/data-source/recent", "regexp": "/^\\/api\\/logs\\/data-source\\/recent(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/logs/data-source", "regexp": "/^\\/api\\/logs\\/data-source(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [], "path": "/api/logs/data-source", "regexp": "/^\\/api\\/logs\\/data-source(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "source", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/cache/stats/:source", "regexp": "/^\\/api\\/cache\\/stats\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "source", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/cache/details/:source", "regexp": "/^\\/api\\/cache\\/details\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "dataSource", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/cache/source/:dataSource", "regexp": "/^\\/api\\/cache\\/source\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [], "path": "/api/cache/key", "regexp": "/^\\/api\\/cache\\/key(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/cache/expire", "regexp": "/^\\/api\\/cache\\/expire(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/cache/clean/time", "regexp": "/^\\/api\\/cache\\/clean\\/time(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/cache/clean/capacity", "regexp": "/^\\/api\\/cache\\/clean\\/capacity(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/cache/clean/auto", "regexp": "/^\\/api\\/cache\\/clean\\/auto(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}]