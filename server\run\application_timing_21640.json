[{"name": "Process Start", "start": 1748231892049, "end": 1748231894263, "duration": 2214, "pid": 21640, "index": 0}, {"name": "Application Start", "start": 1748231894265, "end": 1748231896607, "duration": 2342, "pid": 21640, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748231894292, "end": 1748231894345, "duration": 53, "pid": 21640, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748231894345, "end": 1748231894410, "duration": 65, "pid": 21640, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748231894346, "end": 1748231894347, "duration": 1, "pid": 21640, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748231894352, "end": 1748231894352, "duration": 0, "pid": 21640, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748231894353, "end": 1748231894354, "duration": 1, "pid": 21640, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748231894354, "end": 1748231894355, "duration": 1, "pid": 21640, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748231894360, "end": 1748231894361, "duration": 1, "pid": 21640, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748231894362, "end": 1748231894362, "duration": 0, "pid": 21640, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748231894363, "end": 1748231894364, "duration": 1, "pid": 21640, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748231894365, "end": 1748231894366, "duration": 1, "pid": 21640, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748231894367, "end": 1748231894368, "duration": 1, "pid": 21640, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748231894369, "end": 1748231894370, "duration": 1, "pid": 21640, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748231894372, "end": 1748231894375, "duration": 3, "pid": 21640, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748231894376, "end": 1748231894376, "duration": 0, "pid": 21640, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748231894377, "end": 1748231894378, "duration": 1, "pid": 21640, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748231894378, "end": 1748231894379, "duration": 1, "pid": 21640, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748231894380, "end": 1748231894381, "duration": 1, "pid": 21640, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748231894382, "end": 1748231894383, "duration": 1, "pid": 21640, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748231894384, "end": 1748231894385, "duration": 1, "pid": 21640, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748231894385, "end": 1748231894386, "duration": 1, "pid": 21640, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748231894387, "end": 1748231894388, "duration": 1, "pid": 21640, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748231894389, "end": 1748231894391, "duration": 2, "pid": 21640, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748231894393, "end": 1748231894395, "duration": 2, "pid": 21640, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748231894396, "end": 1748231894396, "duration": 0, "pid": 21640, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748231894398, "end": 1748231894398, "duration": 0, "pid": 21640, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748231894401, "end": 1748231894401, "duration": 0, "pid": 21640, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748231894404, "end": 1748231894405, "duration": 1, "pid": 21640, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748231894410, "end": 1748231894410, "duration": 0, "pid": 21640, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748231894410, "end": 1748231894410, "duration": 0, "pid": 21640, "index": 30}, {"name": "Load extend/application.js", "start": 1748231894411, "end": 1748231894526, "duration": 115, "pid": 21640, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748231894412, "end": 1748231894413, "duration": 1, "pid": 21640, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748231894414, "end": 1748231894416, "duration": 2, "pid": 21640, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748231894416, "end": 1748231894426, "duration": 10, "pid": 21640, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748231894429, "end": 1748231894439, "duration": 10, "pid": 21640, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748231894442, "end": 1748231894445, "duration": 3, "pid": 21640, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748231894446, "end": 1748231894448, "duration": 2, "pid": 21640, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748231894449, "end": 1748231894516, "duration": 67, "pid": 21640, "index": 38}, {"name": "Load extend/request.js", "start": 1748231894526, "end": 1748231894547, "duration": 21, "pid": 21640, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748231894536, "end": 1748231894538, "duration": 2, "pid": 21640, "index": 40}, {"name": "Load extend/response.js", "start": 1748231894547, "end": 1748231894569, "duration": 22, "pid": 21640, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748231894554, "end": 1748231894560, "duration": 6, "pid": 21640, "index": 42}, {"name": "Load extend/context.js", "start": 1748231894569, "end": 1748231894663, "duration": 94, "pid": 21640, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748231894570, "end": 1748231894594, "duration": 24, "pid": 21640, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748231894595, "end": 1748231894598, "duration": 3, "pid": 21640, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748231894599, "end": 1748231894600, "duration": 1, "pid": 21640, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748231894601, "end": 1748231894638, "duration": 37, "pid": 21640, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748231894643, "end": 1748231894645, "duration": 2, "pid": 21640, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748231894647, "end": 1748231894647, "duration": 0, "pid": 21640, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748231894649, "end": 1748231894652, "duration": 3, "pid": 21640, "index": 50}, {"name": "Load extend/helper.js", "start": 1748231894663, "end": 1748231894719, "duration": 56, "pid": 21640, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748231894664, "end": 1748231894696, "duration": 32, "pid": 21640, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748231894704, "end": 1748231894705, "duration": 1, "pid": 21640, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748231894707, "end": 1748231894708, "duration": 1, "pid": 21640, "index": 54}, {"name": "Load app.js", "start": 1748231894719, "end": 1748231894825, "duration": 106, "pid": 21640, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748231894719, "end": 1748231894720, "duration": 1, "pid": 21640, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748231894721, "end": 1748231894724, "duration": 3, "pid": 21640, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748231894726, "end": 1748231894742, "duration": 16, "pid": 21640, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748231894742, "end": 1748231894758, "duration": 16, "pid": 21640, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748231894759, "end": 1748231894777, "duration": 18, "pid": 21640, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748231894777, "end": 1748231894778, "duration": 1, "pid": 21640, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748231894779, "end": 1748231894781, "duration": 2, "pid": 21640, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748231894781, "end": 1748231894782, "duration": 1, "pid": 21640, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748231894782, "end": 1748231894783, "duration": 1, "pid": 21640, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748231894783, "end": 1748231894784, "duration": 1, "pid": 21640, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748231894785, "end": 1748231894785, "duration": 0, "pid": 21640, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748231894786, "end": 1748231894786, "duration": 0, "pid": 21640, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748231894786, "end": 1748231894787, "duration": 1, "pid": 21640, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748231894787, "end": 1748231894795, "duration": 8, "pid": 21640, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748231894795, "end": 1748231894815, "duration": 20, "pid": 21640, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748231894816, "end": 1748231894821, "duration": 5, "pid": 21640, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748231894843, "end": 1748231896586, "duration": 1743, "pid": 21640, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748231895670, "end": 1748231895760, "duration": 90, "pid": 21640, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748231895695, "end": 1748231896512, "duration": 817, "pid": 21640, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748231895795, "end": 1748231896606, "duration": 811, "pid": 21640, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748231895895, "end": 1748231896587, "duration": 692, "pid": 21640, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748231896017, "end": 1748231896545, "duration": 528, "pid": 21640, "index": 77}, {"name": "Load Service", "start": 1748231896017, "end": 1748231896184, "duration": 167, "pid": 21640, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748231896017, "end": 1748231896184, "duration": 167, "pid": 21640, "index": 79}, {"name": "Load Middleware", "start": 1748231896184, "end": 1748231896366, "duration": 182, "pid": 21640, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748231896184, "end": 1748231896350, "duration": 166, "pid": 21640, "index": 81}, {"name": "Load Controller", "start": 1748231896366, "end": 1748231896413, "duration": 47, "pid": 21640, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748231896366, "end": 1748231896413, "duration": 47, "pid": 21640, "index": 83}, {"name": "Load Router", "start": 1748231896413, "end": 1748231896420, "duration": 7, "pid": 21640, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748231896414, "end": 1748231896414, "duration": 0, "pid": 21640, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748231896416, "end": 1748231896512, "duration": 96, "pid": 21640, "index": 86}]