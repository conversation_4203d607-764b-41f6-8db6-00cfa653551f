[{"name": "Process Start", "start": 1748230514634, "end": 1748230516310, "duration": 1676, "pid": 22228, "index": 0}, {"name": "Application Start", "start": 1748230516312, "end": 1748230518231, "duration": 1919, "pid": 22228, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748230516334, "end": 1748230516369, "duration": 35, "pid": 22228, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748230516369, "end": 1748230516414, "duration": 45, "pid": 22228, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748230516371, "end": 1748230516371, "duration": 0, "pid": 22228, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748230516374, "end": 1748230516374, "duration": 0, "pid": 22228, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748230516375, "end": 1748230516376, "duration": 1, "pid": 22228, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748230516376, "end": 1748230516377, "duration": 1, "pid": 22228, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748230516378, "end": 1748230516379, "duration": 1, "pid": 22228, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748230516380, "end": 1748230516381, "duration": 1, "pid": 22228, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748230516382, "end": 1748230516382, "duration": 0, "pid": 22228, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748230516383, "end": 1748230516384, "duration": 1, "pid": 22228, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748230516385, "end": 1748230516386, "duration": 1, "pid": 22228, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748230516387, "end": 1748230516388, "duration": 1, "pid": 22228, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748230516389, "end": 1748230516390, "duration": 1, "pid": 22228, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748230516390, "end": 1748230516391, "duration": 1, "pid": 22228, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748230516392, "end": 1748230516392, "duration": 0, "pid": 22228, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748230516393, "end": 1748230516393, "duration": 0, "pid": 22228, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748230516394, "end": 1748230516395, "duration": 1, "pid": 22228, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748230516395, "end": 1748230516396, "duration": 1, "pid": 22228, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748230516397, "end": 1748230516397, "duration": 0, "pid": 22228, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748230516398, "end": 1748230516398, "duration": 0, "pid": 22228, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748230516399, "end": 1748230516400, "duration": 1, "pid": 22228, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748230516400, "end": 1748230516401, "duration": 1, "pid": 22228, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748230516401, "end": 1748230516402, "duration": 1, "pid": 22228, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748230516403, "end": 1748230516403, "duration": 0, "pid": 22228, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748230516405, "end": 1748230516405, "duration": 0, "pid": 22228, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748230516407, "end": 1748230516407, "duration": 0, "pid": 22228, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748230516410, "end": 1748230516410, "duration": 0, "pid": 22228, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748230516413, "end": 1748230516413, "duration": 0, "pid": 22228, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748230516413, "end": 1748230516413, "duration": 0, "pid": 22228, "index": 30}, {"name": "Load extend/application.js", "start": 1748230516415, "end": 1748230516510, "duration": 95, "pid": 22228, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748230516416, "end": 1748230516416, "duration": 0, "pid": 22228, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748230516417, "end": 1748230516419, "duration": 2, "pid": 22228, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748230516420, "end": 1748230516425, "duration": 5, "pid": 22228, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748230516427, "end": 1748230516433, "duration": 6, "pid": 22228, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748230516435, "end": 1748230516438, "duration": 3, "pid": 22228, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748230516439, "end": 1748230516441, "duration": 2, "pid": 22228, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748230516442, "end": 1748230516502, "duration": 60, "pid": 22228, "index": 38}, {"name": "Load extend/request.js", "start": 1748230516510, "end": 1748230516527, "duration": 17, "pid": 22228, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748230516518, "end": 1748230516520, "duration": 2, "pid": 22228, "index": 40}, {"name": "Load extend/response.js", "start": 1748230516527, "end": 1748230516544, "duration": 17, "pid": 22228, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748230516534, "end": 1748230516537, "duration": 3, "pid": 22228, "index": 42}, {"name": "Load extend/context.js", "start": 1748230516544, "end": 1748230516613, "duration": 69, "pid": 22228, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748230516545, "end": 1748230516561, "duration": 16, "pid": 22228, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748230516562, "end": 1748230516564, "duration": 2, "pid": 22228, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748230516565, "end": 1748230516566, "duration": 1, "pid": 22228, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748230516567, "end": 1748230516597, "duration": 30, "pid": 22228, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748230516599, "end": 1748230516600, "duration": 1, "pid": 22228, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748230516601, "end": 1748230516602, "duration": 1, "pid": 22228, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748230516603, "end": 1748230516607, "duration": 4, "pid": 22228, "index": 50}, {"name": "Load extend/helper.js", "start": 1748230516613, "end": 1748230516656, "duration": 43, "pid": 22228, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748230516614, "end": 1748230516639, "duration": 25, "pid": 22228, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748230516645, "end": 1748230516646, "duration": 1, "pid": 22228, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748230516647, "end": 1748230516648, "duration": 1, "pid": 22228, "index": 54}, {"name": "Load app.js", "start": 1748230516657, "end": 1748230516751, "duration": 94, "pid": 22228, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748230516657, "end": 1748230516658, "duration": 1, "pid": 22228, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748230516658, "end": 1748230516661, "duration": 3, "pid": 22228, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748230516662, "end": 1748230516676, "duration": 14, "pid": 22228, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748230516677, "end": 1748230516691, "duration": 14, "pid": 22228, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748230516691, "end": 1748230516707, "duration": 16, "pid": 22228, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748230516707, "end": 1748230516708, "duration": 1, "pid": 22228, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748230516709, "end": 1748230516711, "duration": 2, "pid": 22228, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748230516712, "end": 1748230516712, "duration": 0, "pid": 22228, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748230516713, "end": 1748230516714, "duration": 1, "pid": 22228, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748230516714, "end": 1748230516715, "duration": 1, "pid": 22228, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748230516717, "end": 1748230516718, "duration": 1, "pid": 22228, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748230516718, "end": 1748230516719, "duration": 1, "pid": 22228, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748230516719, "end": 1748230516720, "duration": 1, "pid": 22228, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748230516720, "end": 1748230516725, "duration": 5, "pid": 22228, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748230516726, "end": 1748230516744, "duration": 18, "pid": 22228, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748230516744, "end": 1748230516750, "duration": 6, "pid": 22228, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748230516764, "end": 1748230518212, "duration": 1448, "pid": 22228, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748230517450, "end": 1748230517527, "duration": 77, "pid": 22228, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748230517471, "end": 1748230518157, "duration": 686, "pid": 22228, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748230517562, "end": 1748230518230, "duration": 668, "pid": 22228, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748230517658, "end": 1748230518214, "duration": 556, "pid": 22228, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748230517753, "end": 1748230518177, "duration": 424, "pid": 22228, "index": 77}, {"name": "Load Service", "start": 1748230517754, "end": 1748230517877, "duration": 123, "pid": 22228, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748230517754, "end": 1748230517877, "duration": 123, "pid": 22228, "index": 79}, {"name": "Load Middleware", "start": 1748230517877, "end": 1748230518044, "duration": 167, "pid": 22228, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748230517878, "end": 1748230518029, "duration": 151, "pid": 22228, "index": 81}, {"name": "Load Controller", "start": 1748230518044, "end": 1748230518086, "duration": 42, "pid": 22228, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748230518044, "end": 1748230518086, "duration": 42, "pid": 22228, "index": 83}, {"name": "Load Router", "start": 1748230518086, "end": 1748230518093, "duration": 7, "pid": 22228, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748230518087, "end": 1748230518088, "duration": 1, "pid": 22228, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748230518089, "end": 1748230518157, "duration": 68, "pid": 22228, "index": 86}]