[{"name": "Process Start", "start": 1746258502139, "end": 1746258503982, "duration": 1843, "pid": 900, "index": 0}, {"name": "Application Start", "start": 1746258503983, "end": 1746258506021, "duration": 2038, "pid": 900, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1746258504003, "end": 1746258504037, "duration": 34, "pid": 900, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1746258504038, "end": 1746258504080, "duration": 42, "pid": 900, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1746258504039, "end": 1746258504039, "duration": 0, "pid": 900, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1746258504041, "end": 1746258504041, "duration": 0, "pid": 900, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1746258504043, "end": 1746258504043, "duration": 0, "pid": 900, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1746258504044, "end": 1746258504044, "duration": 0, "pid": 900, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1746258504046, "end": 1746258504047, "duration": 1, "pid": 900, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1746258504047, "end": 1746258504048, "duration": 1, "pid": 900, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1746258504049, "end": 1746258504049, "duration": 0, "pid": 900, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1746258504050, "end": 1746258504050, "duration": 0, "pid": 900, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1746258504051, "end": 1746258504052, "duration": 1, "pid": 900, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1746258504052, "end": 1746258504053, "duration": 1, "pid": 900, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1746258504054, "end": 1746258504054, "duration": 0, "pid": 900, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1746258504055, "end": 1746258504055, "duration": 0, "pid": 900, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1746258504056, "end": 1746258504056, "duration": 0, "pid": 900, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1746258504057, "end": 1746258504057, "duration": 0, "pid": 900, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1746258504058, "end": 1746258504058, "duration": 0, "pid": 900, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1746258504059, "end": 1746258504060, "duration": 1, "pid": 900, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1746258504060, "end": 1746258504061, "duration": 1, "pid": 900, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1746258504062, "end": 1746258504063, "duration": 1, "pid": 900, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1746258504064, "end": 1746258504064, "duration": 0, "pid": 900, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1746258504065, "end": 1746258504066, "duration": 1, "pid": 900, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1746258504066, "end": 1746258504067, "duration": 1, "pid": 900, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1746258504068, "end": 1746258504068, "duration": 0, "pid": 900, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1746258504070, "end": 1746258504070, "duration": 0, "pid": 900, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1746258504072, "end": 1746258504073, "duration": 1, "pid": 900, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1746258504075, "end": 1746258504076, "duration": 1, "pid": 900, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1746258504079, "end": 1746258504079, "duration": 0, "pid": 900, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1746258504079, "end": 1746258504079, "duration": 0, "pid": 900, "index": 30}, {"name": "Load extend/application.js", "start": 1746258504081, "end": 1746258504186, "duration": 105, "pid": 900, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1746258504082, "end": 1746258504083, "duration": 1, "pid": 900, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1746258504084, "end": 1746258504086, "duration": 2, "pid": 900, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1746258504086, "end": 1746258504092, "duration": 6, "pid": 900, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1746258504094, "end": 1746258504104, "duration": 10, "pid": 900, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1746258504105, "end": 1746258504108, "duration": 3, "pid": 900, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1746258504109, "end": 1746258504111, "duration": 2, "pid": 900, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1746258504113, "end": 1746258504177, "duration": 64, "pid": 900, "index": 38}, {"name": "Load extend/request.js", "start": 1746258504186, "end": 1746258504202, "duration": 16, "pid": 900, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1746258504193, "end": 1746258504195, "duration": 2, "pid": 900, "index": 40}, {"name": "Load extend/response.js", "start": 1746258504202, "end": 1746258504221, "duration": 19, "pid": 900, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1746258504210, "end": 1746258504214, "duration": 4, "pid": 900, "index": 42}, {"name": "Load extend/context.js", "start": 1746258504221, "end": 1746258504293, "duration": 72, "pid": 900, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1746258504222, "end": 1746258504241, "duration": 19, "pid": 900, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1746258504241, "end": 1746258504244, "duration": 3, "pid": 900, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1746258504245, "end": 1746258504246, "duration": 1, "pid": 900, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1746258504247, "end": 1746258504276, "duration": 29, "pid": 900, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1746258504278, "end": 1746258504280, "duration": 2, "pid": 900, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1746258504282, "end": 1746258504282, "duration": 0, "pid": 900, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1746258504284, "end": 1746258504287, "duration": 3, "pid": 900, "index": 50}, {"name": "Load extend/helper.js", "start": 1746258504294, "end": 1746258504339, "duration": 45, "pid": 900, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1746258504295, "end": 1746258504324, "duration": 29, "pid": 900, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1746258504330, "end": 1746258504331, "duration": 1, "pid": 900, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1746258504331, "end": 1746258504332, "duration": 1, "pid": 900, "index": 54}, {"name": "Load app.js", "start": 1746258504340, "end": 1746258504433, "duration": 93, "pid": 900, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1746258504340, "end": 1746258504340, "duration": 0, "pid": 900, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1746258504341, "end": 1746258504344, "duration": 3, "pid": 900, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1746258504346, "end": 1746258504358, "duration": 12, "pid": 900, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1746258504359, "end": 1746258504376, "duration": 17, "pid": 900, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1746258504377, "end": 1746258504392, "duration": 15, "pid": 900, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1746258504392, "end": 1746258504393, "duration": 1, "pid": 900, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1746258504394, "end": 1746258504396, "duration": 2, "pid": 900, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1746258504397, "end": 1746258504397, "duration": 0, "pid": 900, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1746258504398, "end": 1746258504398, "duration": 0, "pid": 900, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1746258504399, "end": 1746258504399, "duration": 0, "pid": 900, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1746258504400, "end": 1746258504401, "duration": 1, "pid": 900, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1746258504401, "end": 1746258504402, "duration": 1, "pid": 900, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1746258504402, "end": 1746258504402, "duration": 0, "pid": 900, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1746258504403, "end": 1746258504408, "duration": 5, "pid": 900, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1746258504409, "end": 1746258504425, "duration": 16, "pid": 900, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1746258504426, "end": 1746258504431, "duration": 5, "pid": 900, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1746258504445, "end": 1746258506000, "duration": 1555, "pid": 900, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1746258505158, "end": 1746258505248, "duration": 90, "pid": 900, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1746258505186, "end": 1746258505940, "duration": 754, "pid": 900, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1746258505282, "end": 1746258506021, "duration": 739, "pid": 900, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1746258505385, "end": 1746258506002, "duration": 617, "pid": 900, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1746258505497, "end": 1746258505965, "duration": 468, "pid": 900, "index": 77}, {"name": "Load Service", "start": 1746258505497, "end": 1746258505627, "duration": 130, "pid": 900, "index": 78}, {"name": "Load \"service\" to Context", "start": 1746258505498, "end": 1746258505627, "duration": 129, "pid": 900, "index": 79}, {"name": "Load Middleware", "start": 1746258505628, "end": 1746258505810, "duration": 182, "pid": 900, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1746258505628, "end": 1746258505792, "duration": 164, "pid": 900, "index": 81}, {"name": "Load Controller", "start": 1746258505810, "end": 1746258505858, "duration": 48, "pid": 900, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1746258505810, "end": 1746258505858, "duration": 48, "pid": 900, "index": 83}, {"name": "Load Router", "start": 1746258505858, "end": 1746258505866, "duration": 8, "pid": 900, "index": 84}, {"name": "Require(62) app/router.js", "start": 1746258505859, "end": 1746258505860, "duration": 1, "pid": 900, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1746258505861, "end": 1746258505940, "duration": 79, "pid": 900, "index": 86}]